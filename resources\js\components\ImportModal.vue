<template>
  <Dialog :open="show" @update:open="emit('update:show', $event)">
    <DialogContent class="sm:max-w-lg">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon name="upload" class="w-5 h-5" />
          Import {{ title }}
        </DialogTitle>
        <DialogDescription>
          Upload file Excel atau CSV untuk import data {{ title.toLowerCase() }}.
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- Template Download -->
        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
          <div class="flex items-start gap-3">
            <Icon name="info" class="w-5 h-5 text-blue-600 mt-0.5" />
            <div class="flex-1">
              <h4 class="font-medium text-blue-900">Download Template</h4>
              <p class="text-sm text-blue-700 mt-1">
                Gunakan template ini untuk memastikan format data yang benar.
              </p>
              <Button
                variant="outline"
                size="sm"
                class="mt-2 border-blue-300 text-blue-700 hover:bg-blue-100"
                @click="downloadTemplate"
                :disabled="isDownloadingTemplate"
              >
                <Icon v-if="isDownloadingTemplate" name="loader" class="w-4 h-4 mr-2 animate-spin" />
                <Icon v-else name="download" class="w-4 h-4 mr-2" />
                {{ isDownloadingTemplate ? 'Downloading...' : 'Download Template' }}
              </Button>
            </div>
          </div>
        </div>

        <!-- File Upload -->
        <div class="space-y-2">
          <Label for="file">Pilih File</Label>
          <div class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <input
              id="file"
              ref="fileInput"
              type="file"
              accept=".xlsx,.xls,.csv"
              class="hidden"
              @change="handleFileSelect"
            />

            <div v-if="!selectedFile" class="space-y-2">
              <Icon name="upload" class="w-8 h-8 text-gray-400 mx-auto" />
              <div>
                <Button variant="outline" @click="$refs.fileInput.click()">
                  Pilih File
                </Button>
                <p class="text-sm text-gray-500 mt-2">
                  atau drag & drop file di sini
                </p>
              </div>
              <p class="text-xs text-gray-400">
                Format: Excel (.xlsx, .xls) atau CSV (.csv), maksimal 5MB
              </p>
            </div>

            <div v-else class="space-y-2">
              <Icon name="file" class="w-8 h-8 text-green-600 mx-auto" />
              <div>
                <p class="font-medium text-gray-900">{{ selectedFile.name }}</p>
                <p class="text-sm text-gray-500">{{ formatFileSize(selectedFile.size) }}</p>
              </div>
              <Button variant="outline" size="sm" @click="clearFile">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Hapus File
              </Button>
            </div>
          </div>
          <InputError :message="form.errors.file" />
        </div>

        <!-- Import Options -->
        <div class="space-y-3">
          <Label>Opsi Import</Label>
          <div class="space-y-2">
            <label class="flex items-center space-x-2">
              <input
                v-model="importOptions.updateExisting"
                type="checkbox"
                class="rounded border-gray-300"
              />
              <span class="text-sm">Update data yang sudah ada</span>
            </label>
            <label class="flex items-center space-x-2">
              <input
                v-model="importOptions.skipErrors"
                type="checkbox"
                class="rounded border-gray-300"
              />
              <span class="text-sm">Lewati baris yang error</span>
            </label>
          </div>
        </div>
      </div>

      <DialogFooter class="flex justify-between">
        <Button variant="outline" @click="emit('update:show', false)">
          Batal
        </Button>
        <Button
          @click="handleImport"
          :disabled="!selectedFile || isImporting"
        >
          <Icon v-if="isImporting" name="loader" class="w-4 h-4 mr-2 animate-spin" />
          <Icon v-else name="upload" class="w-4 h-4 mr-2" />
          {{ isImporting ? 'Mengimport...' : 'Import' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>

  <!-- Progress Modal -->
  <ImportProgressModal
    :show="showProgressModal"
    @update:show="showProgressModal = $event"
    :title="`Import ${title}`"
    :progress="importProgress"
    :status="importStatus"
    :results="importResults"
    @close="handleProgressClose"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@inertiajs/vue3'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Label } from '@/components/ui/label'
import Icon from '@/components/Icon.vue'
import InputError from '@/components/InputError.vue'
import ImportProgressModal from './ImportProgressModal.vue'

// Props
interface Props {
  show: boolean
  title: string
  importUrl: string
  templateUrl: string
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'imported': [results: any]
}>()

// Reactive data
const selectedFile = ref<File | null>(null)
const isImporting = ref(false)
const isDownloadingTemplate = ref(false)
const showProgressModal = ref(false)
const importProgress = ref(0)
const importStatus = ref<'idle' | 'processing' | 'completed' | 'error'>('idle')
const importResults = ref<any>(null)

const importOptions = ref({
  updateExisting: true,
  skipErrors: true
})

const form = useForm({
  file: null as File | null,
  options: importOptions.value
})

// Methods
const handleFileSelect = (event: Event) => {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // Validate file size (5MB)
    if (file.size > 5 * 1024 * 1024) {
      form.setError('file', 'Ukuran file tidak boleh lebih dari 5MB')
      return
    }

    // Validate file type
    const allowedTypes = [
      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
      'application/vnd.ms-excel',
      'text/csv'
    ]

    if (!allowedTypes.includes(file.type)) {
      form.setError('file', 'Format file harus Excel (.xlsx, .xls) atau CSV (.csv)')
      return
    }

    selectedFile.value = file
    form.file = file
    form.clearErrors('file')
  }
}

const clearFile = () => {
  selectedFile.value = null
  form.file = null
  form.clearErrors('file')

  // Reset file input
  const fileInput = document.getElementById('file') as HTMLInputElement
  if (fileInput) {
    fileInput.value = ''
  }
}

const downloadTemplate = async () => {
  isDownloadingTemplate.value = true

  try {
    window.open(props.templateUrl, '_blank')
  } catch (error) {
    console.error('Template download error:', error)
  } finally {
    isDownloadingTemplate.value = false
  }
}

const handleImport = () => {
  if (!selectedFile.value) return

  isImporting.value = true
  showProgressModal.value = true
  importStatus.value = 'processing'
  importProgress.value = 0

  // Simulate progress
  const progressInterval = setInterval(() => {
    if (importProgress.value < 90) {
      importProgress.value += Math.random() * 20
    }
  }, 500)

  // Debug: Log form data before submission
  console.log('Submitting import form:', {
    url: props.importUrl,
    file: form.file,
    fileName: form.file?.name,
    fileSize: form.file?.size,
    fileType: form.file?.type,
    csrfToken: document.querySelector('meta[name="csrf-token"]')?.getAttribute('content')
  })

  // Test with debug endpoint first
  const debugUrl = props.importUrl.replace('/import', '/import-debug')

  form.post(debugUrl, {
    onSuccess: (response) => {
      console.log('Debug test success:', response)

      // If debug test passes, try the actual import
      form.post(props.importUrl, {
        onSuccess: (response) => {
          console.log('Import success:', response)
          clearInterval(progressInterval)
          importProgress.value = 100
          importStatus.value = 'completed'
          importResults.value = response.props?.flash || {}

          emit('imported', importResults.value)
        },
        onError: (errors) => {
          console.error('Import error:', errors)
          clearInterval(progressInterval)
          importStatus.value = 'error'
          importResults.value = { errors }
        },
        onFinish: () => {
          isImporting.value = false
        }
      })
    },
    onError: (errors) => {
      console.error('Debug test failed:', errors)
      clearInterval(progressInterval)
      importStatus.value = 'error'
      importResults.value = {
        errors,
        debug_message: 'Debug test failed - check console for details'
      }
      isImporting.value = false
    }
  })
}

const handleProgressClose = () => {
  showProgressModal.value = false
  emit('update:show', false)

  // Reset form
  clearFile()
  importProgress.value = 0
  importStatus.value = 'idle'
  importResults.value = null
}

const formatFileSize = (bytes: number): string => {
  if (bytes === 0) return '0 Bytes'

  const k = 1024
  const sizes = ['Bytes', 'KB', 'MB', 'GB']
  const i = Math.floor(Math.log(bytes) / Math.log(k))

  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
}
</script>
