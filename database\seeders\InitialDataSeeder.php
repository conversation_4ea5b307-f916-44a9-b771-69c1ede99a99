<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;

class InitialDataSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Insert wilayah Lampung
        DB::table('wilayah')->insert([
            ['kode_wilayah' => '18', 'nama_wilayah' => 'Lampung', 'level_wilayah' => 'provinsi', 'parent_id' => null],
            ['kode_wilayah' => '1801', 'nama_wilayah' => 'Lampung Barat', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1802', 'nama_wilayah' => 'Lampung Selatan', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1803', 'nama_wilayah' => 'Lampung Tengah', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1804', 'nama_wilayah' => 'Lampung Utara', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1805', 'nama_wilayah' => 'Lampung Timur', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1806', 'nama_wilayah' => 'Way Kanan', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1807', 'nama_wilayah' => 'Tulang Bawang', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1808', 'nama_wilayah' => 'Pesawaran', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1809', 'nama_wilayah' => 'Pringsewu', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1810', 'nama_wilayah' => 'Mesuji', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1811', 'nama_wilayah' => 'Tulang Bawang Barat', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1812', 'nama_wilayah' => 'Pesisir Barat', 'level_wilayah' => 'kabupaten', 'parent_id' => 1],
            ['kode_wilayah' => '1871', 'nama_wilayah' => 'Bandar Lampung', 'level_wilayah' => 'kota', 'parent_id' => 1],
            ['kode_wilayah' => '1872', 'nama_wilayah' => 'Metro', 'level_wilayah' => 'kota', 'parent_id' => 1],
        ]);

        // // Insert cabang lomba
        // DB::table('cabang_lomba')->insert([
        //     ['kode_cabang' => 'TIL', 'nama_cabang' => 'Tilawatil Quran', 'deskripsi' => 'Seni membaca Al-Quran dengan tartil'],
        //     ['kode_cabang' => 'TAH', 'nama_cabang' => 'Tahfidzul Quran', 'deskripsi' => 'Hafalan Al-Quran'],
        //     ['kode_cabang' => 'FAH', 'nama_cabang' => 'Fahmil Quran', 'deskripsi' => 'Pemahaman isi Al-Quran'],
        //     ['kode_cabang' => 'SYA', 'nama_cabang' => 'Syarhil Quran', 'deskripsi' => 'Tafsir Al-Quran'],
        //     ['kode_cabang' => 'KAL', 'nama_cabang' => 'Kaligrafi', 'deskripsi' => 'Seni tulis Arab'],
        //     ['kode_cabang' => 'NAS', 'nama_cabang' => 'Nasyid', 'deskripsi' => 'Seni musik Islami'],
        //     ['kode_cabang' => 'CER', 'nama_cabang' => 'Ceramah', 'deskripsi' => 'Dakwah dan khutbah'],
        //     ['kode_cabang' => 'QIS', 'nama_cabang' => 'Qiraatul Kutub', 'deskripsi' => 'Pembacaan kitab kuning'],
        // ]);

        // // Insert golongan untuk beberapa cabang
        // DB::table('golongan')->insert([
        //     ['kode_golongan' => 'TIL-PA', 'nama_golongan' => 'Tilawah Putra Anak-anak', 'id_cabang' => 1, 'jenis_kelamin' => 'L', 'batas_umur_min' => 10, 'batas_umur_max' => 12, 'biaya_pendaftaran' => 50000],
        //     ['kode_golongan' => 'TIL-PI', 'nama_golongan' => 'Tilawah Putri Anak-anak', 'id_cabang' => 1, 'jenis_kelamin' => 'P', 'batas_umur_min' => 10, 'batas_umur_max' => 12, 'biaya_pendaftaran' => 50000],
        //     ['kode_golongan' => 'TIL-PR', 'nama_golongan' => 'Tilawah Putra Remaja', 'id_cabang' => 1, 'jenis_kelamin' => 'L', 'batas_umur_min' => 13, 'batas_umur_max' => 16, 'biaya_pendaftaran' => 75000],
        //     ['kode_golongan' => 'TIL-PiR', 'nama_golongan' => 'Tilawah Putri Remaja', 'id_cabang' => 1, 'jenis_kelamin' => 'P', 'batas_umur_min' => 13, 'batas_umur_max' => 16, 'biaya_pendaftaran' => 75000],
        //     ['kode_golongan' => 'TIL-PD', 'nama_golongan' => 'Tilawah Putra Dewasa', 'id_cabang' => 1, 'jenis_kelamin' => 'L', 'batas_umur_min' => 17, 'batas_umur_max' => 35, 'biaya_pendaftaran' => 100000],
        //     ['kode_golongan' => 'TIL-PiD', 'nama_golongan' => 'Tilawah Putri Dewasa', 'id_cabang' => 1, 'jenis_kelamin' => 'P', 'batas_umur_min' => 17, 'batas_umur_max' => 35, 'biaya_pendaftaran' => 100000],
        //     ['kode_golongan' => 'TAH-5P', 'nama_golongan' => 'Tahfidz 5 Juz Putra', 'id_cabang' => 2, 'jenis_kelamin' => 'L', 'batas_umur_min' => 15, 'batas_umur_max' => 25, 'biaya_pendaftaran' => 100000],
        //     ['kode_golongan' => 'TAH-5Pi', 'nama_golongan' => 'Tahfidz 5 Juz Putri', 'id_cabang' => 2, 'jenis_kelamin' => 'P', 'batas_umur_min' => 15, 'batas_umur_max' => 25, 'biaya_pendaftaran' => 100000],
        //     ['kode_golongan' => 'TAH-10P', 'nama_golongan' => 'Tahfidz 10 Juz Putra', 'id_cabang' => 2, 'jenis_kelamin' => 'L', 'batas_umur_min' => 18, 'batas_umur_max' => 30, 'biaya_pendaftaran' => 150000],
        //     ['kode_golongan' => 'TAH-10Pi', 'nama_golongan' => 'Tahfidz 10 Juz Putri', 'id_cabang' => 2, 'jenis_kelamin' => 'P', 'batas_umur_min' => 18, 'batas_umur_max' => 30, 'biaya_pendaftaran' => 150000],
        // ]);

        // // Insert mimbar
        // DB::table('mimbar')->insert([
        //     ['kode_mimbar' => 'A', 'nama_mimbar' => 'Mimbar A', 'kapasitas' => 50],
        //     ['kode_mimbar' => 'B', 'nama_mimbar' => 'Mimbar B', 'kapasitas' => 50],
        //     ['kode_mimbar' => 'C', 'nama_mimbar' => 'Mimbar C', 'kapasitas' => 50],
        //     ['kode_mimbar' => 'D', 'nama_mimbar' => 'Mimbar D', 'kapasitas' => 50],
        // ]);

        // Insert jenis nilai
        DB::table('jenis_nilai')->insert([
            ['nama_jenis' => 'Fashohah', 'kode_jenis' => 'FAS', 'keterangan' => 'Kebenaran dalam membaca', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Nagham', 'kode_jenis' => 'NAG', 'keterangan' => 'Keindahan suara dan lagu', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Tajwid', 'kode_jenis' => 'TAJ', 'keterangan' => 'Kaidah tajwid', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Adab', 'kode_jenis' => 'ADA', 'keterangan' => 'Etika dan sopan santun', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Penampilan', 'kode_jenis' => 'PEN', 'keterangan' => 'Penampilan dan busana', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Penguasaan Materi', 'kode_jenis' => 'PEN_MAT', 'keterangan' => 'Penguasaan materi yang disampaikan', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Sistematika', 'kode_jenis' => 'SIS', 'keterangan' => 'Sistematika penyampaian', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Bahasa', 'kode_jenis' => 'BAH', 'keterangan' => 'Penggunaan bahasa yang baik', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Kreativitas', 'kode_jenis' => 'KRE', 'keterangan' => 'Kreativitas dalam penyampaian', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
            ['nama_jenis' => 'Ketepatan Waktu', 'kode_jenis' => 'WAK', 'keterangan' => 'Ketepatan waktu dalam penyampaian', 'bobot_nilai' => 1.0, 'nilai_maksimum' => 100],
        ]);

        // Insert user default (superadmin)
        DB::table('users')->insert([
            [
                'username' => 'superadmin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'superadmin',
                'nama_lengkap' => 'Administrator MTQ Lampung',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin',
                'nama_lengkap' => 'Admin MTQ Lampung',
                'status' => 'aktif'
            ]
        ]);

        // Insert sample admin daerah
        DB::table('users')->insert([
            [
                'username' => 'admin_bdl',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 14, // Bandar Lampung
                'nama_lengkap' => 'Admin Bandar Lampung',
                'no_telepon' => '08111222333',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_metro',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 15, // Metro
                'nama_lengkap' => 'Admin Metro',
                'no_telepon' => '08111222334',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_lamsel',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 3, // Lampung Selatan
                'nama_lengkap' => 'Admin Lampung Selatan',
                'no_telepon' => '08111222335',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_lambar',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 2, // Lampung Barat
                'nama_lengkap' => 'Admin Lampung Barat',
                'no_telepon' => '08111222336',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_lamut',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 1, // Lampung Utara
                'nama_lengkap' => 'Admin Lampung Utara',
                'no_telepon' => '08111222337',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_tengah',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 4, // Lampung Tengah
                'nama_lengkap' => 'Admin Lampung Tengah',
                'no_telepon' => '08111222338',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_lamtim',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 5, // Lampung Timur
                'nama_lengkap' => 'Admin Lampung Timur',
                'no_telepon' => '08111222339',
                'status' => 'aktif'
            ],
            [
                'username' => 'admin_lambarat',
                'email' => '<EMAIL>',
                'password' => Hash::make('password'),
                'role' => 'admin_daerah',
                'id_wilayah' => 6, // Lampung Barat
                'nama_lengkap' => 'Admin Lampung Barat',
                'no_telepon' => '08111222340',
                'status' => 'aktif'
            ]
        ]);
    }
}
