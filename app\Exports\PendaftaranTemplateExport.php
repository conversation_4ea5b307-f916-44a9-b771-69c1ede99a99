<?php

namespace App\Exports;

use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\Mimbar;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class PendaftaranTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    protected $userRole;
    protected $userWilayah;

    public function __construct($userRole = null, $userWilayah = null)
    {
        $this->userRole = $userRole;
        $this->userWilayah = $userWilayah;
    }

    /**
     * @return array
     */
    public function array(): array
    {
        // Get sample data for examples
        $pesertaQuery = Peserta::with('wilayah');
        
        if ($this->userRole === 'admin_daerah' && $this->userWilayah) {
            $pesertaQuery->where('id_wilayah', $this->userWilayah);
        }
        
        $samplePeserta = $pesertaQuery->limit(3)->get();
        $sampleGolongan = Golongan::with('cabangLomba')->limit(3)->get();
        $sampleMimbar = Mimbar::limit(3)->get();

        $currentYear = date('Y');

        return [
            [
                $samplePeserta[0]->nik ?? '1234567890123456',
                $sampleGolongan[0]->nama_golongan ?? 'Tilawah Anak-anak Putra',
                $sampleMimbar[0]->nama_mimbar ?? 'Mimbar 1',
                $currentYear,
                'draft',
                'pending',
                'Pendaftaran untuk lomba tilawah'
            ],
            [
                $samplePeserta[1]->nik ?? '9876543210987654',
                $sampleGolongan[1]->nama_golongan ?? 'Hifzil Quran Remaja Putri',
                $sampleMimbar[1]->nama_mimbar ?? 'Mimbar 2',
                $currentYear,
                'submitted',
                'verified',
                'Pendaftaran untuk lomba hifzil quran'
            ],
            [
                $samplePeserta[2]->nik ?? '5555666677778888',
                $sampleGolongan[2]->nama_golongan ?? 'Fahmil Quran Dewasa',
                $sampleMimbar[2]->nama_mimbar ?? 'Mimbar 3',
                $currentYear,
                'approved',
                'verified',
                'Pendaftaran untuk lomba fahmil quran'
            ]
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'nik_peserta',
            'golongan',
            'mimbar',
            'tahun_pendaftaran',
            'status_pendaftaran',
            'regional_verification_status',
            'keterangan'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        // Add instructions in the first few rows
        $sheet->insertNewRowBefore(1, 15);
        
        $sheet->setCellValue('A1', 'TEMPLATE IMPORT PENDAFTARAN MTQ');
        $sheet->setCellValue('A2', 'Petunjuk Penggunaan:');
        $sheet->setCellValue('A3', '1. Isi data mulai dari baris 17 (setelah header)');
        $sheet->setCellValue('A4', '2. Kolom nik_peserta: NIK peserta yang sudah terdaftar (16 digit)');
        $sheet->setCellValue('A5', '3. Kolom golongan: Nama golongan yang sudah ada di sistem');
        $sheet->setCellValue('A6', '4. Kolom mimbar: Nama mimbar yang sudah ada (opsional)');
        $sheet->setCellValue('A7', '5. Kolom tahun_pendaftaran: Tahun pendaftaran (format YYYY)');
        $sheet->setCellValue('A8', '6. Kolom status_pendaftaran: "draft", "submitted", "verified", "approved", atau "rejected"');
        $sheet->setCellValue('A9', '7. Kolom regional_verification_status: "pending", "verified", atau "rejected"');
        $sheet->setCellValue('A10', '8. Kolom keterangan: Catatan tambahan (opsional)');
        $sheet->setCellValue('A11', '');
        $sheet->setCellValue('A12', 'CATATAN PENTING:');
        $sheet->setCellValue('A13', '- Peserta harus sudah terdaftar di sistem sebelum membuat pendaftaran');
        $sheet->setCellValue('A14', '- Golongan dan mimbar harus sesuai dengan data yang ada di sistem');
        $sheet->setCellValue('A15', '- Admin daerah hanya dapat mendaftarkan peserta dari wilayahnya');

        return [
            // Title style
            1 => [
                'font' => ['bold' => true, 'size' => 14],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            // Instructions style
            '2:15' => [
                'font' => ['italic' => true, 'size' => 10],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFFEF3C7']
                ]
            ],
            // Important notes style
            '12:15' => [
                'font' => ['bold' => true, 'italic' => true, 'size' => 10],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFFECACA']
                ]
            ],
            // Header style
            16 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }
}
