[2025-08-05 06:11:56] local.INFO: Golongan Import Request {"user_id":1,"file_name":"template-import-golongan.xlsx","file_size":12385,"timestamp":"2025-08-05 06:11:56"} 
[2025-08-05 06:11:56] local.INFO: Golongan Import Results {"user_id":1,"results":{"success":0,"failed":58,"errors":[{"row":{"template_import_golongan_lomba":"Petunjuk Penggunaan:","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"1. Isi data mulai dari baris 10 (setelah header)","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"<PERSON>is kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"2. <PERSON>lom cabang_lomba: Harus sesuai dengan nama cabang yang sudah ada","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"3. Kolom jenis_kelamin: \"Laki-laki\" atau \"Perempuan\"","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"4. Kolom batas_umur_min/max: Angka dalam tahun","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"5. Kolom biaya_pendaftaran: Angka tanpa titik/koma","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"6. Kolom nomor_urut: Boleh kosong, akan diisi otomatis","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"kode_golongan","1":"nama_golongan","2":"cabang_lomba","3":"jenis_kelamin","4":"batas_umur_min","5":"batas_umur_max","6":"kuota_maksimal","7":"biaya_pendaftaran","8":"nomor_urut_awal","9":"nomor_urut_akhir","10":"status"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ2A","1":"Anak-anak Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":14,"6":50,"7":1,"8":30,"9":50,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ2B","1":"Anak-anak Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":14,"6":50,"7":1,"8":30,"9":100,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ4A","1":"Cacat Netra Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":49,"6":30,"7":211,"8":240,"9":130,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ4B","1":"Cacat Netra Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":49,"6":50,"7":211,"8":240,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ5A","1":"Dewasa Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":40,"6":50,"7":61,"8":90,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ6A","1":"Qira'at Mujawad Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":40,"6":50,"7":181,"8":210,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ6B","1":"Qira'at Mujawad Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":40,"6":50,"7":181,"8":210,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ7A","1":"Qira'at Murattal Dewasa Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":40,"6":50,"7":151,"8":180,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ7B","1":"Qira'at Murattal Dewasa Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":40,"6":50,"7":151,"8":180,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ1A","1":"1 Juz Tilawah Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":16,"6":50,"7":241,"8":270,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ1B","1":"1 Juz Tilawah Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":15,"6":50,"7":241,"8":270,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ2A","1":"5 Juz dan Tilawah Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":20,"6":50,"7":271,"8":300,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ2B","1":"5 Juz dan Tilawah Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":20,"6":50,"7":271,"8":300,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ3A","1":"10 Juz Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":301,"8":330,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ3B","1":"10 Juz Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":301,"8":330,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ4A","1":"20 Juz Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":331,"8":360,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ4B","1":"20 Juz Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":331,"8":360,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ5A","1":"30 Juz Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":361,"8":390,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ5B","1":"30 Juz Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":361,"8":390,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF1A","1":"Bahasa Arab Putra","2":"Tafsir Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":421,"8":450,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF1B","1":"Bahasa Arab Putri","2":"Tafsir Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":421,"8":450,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF2A","1":"Bahasa Indonesia Putra","2":"Tafsir Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":391,"8":420,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF2B","1":"Bahasa Indonesia Putri","2":"Tafsir Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":391,"8":420,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"FQ1A","1":"Fahm Al-Quran Putra","2":"Fahm Al-Quran","3":"Laki-laki","4":6,"5":18,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"FQ1B","1":"Fahm Al-Quran Putri","2":"Fahm Al-Quran","3":"Perempuan","4":6,"5":18,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"SQ1A","1":"Syarhil Qur'an Putra","2":"Syarh Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"SQ1B","1":"Syarh Al-Quran Putri","2":"Syarh Al-Quran","3":"Perempuan","4":6,"5":18,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ1A","1":"Naskah Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":481,"8":510,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ1B","1":"Naskah Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":481,"8":510,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ2A","1":"Hiasan Mushaf Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":511,"8":540,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ2B","1":"Hiasan Mushaf Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":511,"8":540,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ3A","1":"Dekorasi Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":541,"8":570,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ3B","1":"Dekorasi Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":541,"8":570,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ4A","1":"Kontemporer Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":571,"8":600,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ4B","1":"Kontemporer Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":571,"8":600,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"MQ1A","1":"Makalah Putra","2":"\tMakalah Ilmiah Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":601,"8":630,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"MQ1B","1":"Makalah Putri","2":"\tMakalah Ilmiah Al-Quran","3":"Perempuan","4":6,"5":24,"6":50,"7":601,"8":630,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ8A","1":"Qiroat Murottal Remaja Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":121,"8":150,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ8B","1":"Qiroat Murottal Remaja Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":24,"6":50,"7":121,"8":150,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ5B","1":"Dewasa Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":40,"6":50,"7":61,"8":90,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF3A","1":"Bahasa Inggris Putra","2":"Tafsir Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":451,"8":480,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF3B","1":"Bahasa Inggris Putri","2":"Tafsir Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":451,"8":480,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH1A","1":"Hafalan 100 Hadist Putra","2":"\tHafalan Hadits","3":"Laki-laki","4":6,"5":22,"6":50,"7":631,"8":660,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH1B","1":"Hafalan 100 Hadist Putri","2":"\tHafalan Hadits","3":"Perempuan","4":6,"5":22,"6":50,"7":631,"8":660,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH2A","1":"Hafalan 500 Hadist Putra","2":"\tHafalan Hadits","3":"Laki-laki","4":6,"5":22,"6":50,"7":661,"8":690,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH2B","1":"Hafalan 500 Hadist Putri","2":"\tHafalan Hadits","3":"Perempuan","4":6,"5":22,"6":50,"7":661,"8":690,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ3A","1":"Remaja Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":31,"8":60,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ3B","1":"Remaja Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":24,"6":50,"7":31,"8":60,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ1A","1":"Tartil Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":13,"6":50,"7":91,"8":120,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ1B","1":"Tartil Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":12,"6":50,"7":91,"8":120,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"}],"created":[],"updated":[]},"timestamp":"2025-08-05 06:11:56"} 
[2025-08-05 06:11:57] local.INFO: Golongan Import Request {"user_id":1,"file_name":"template-import-golongan.xlsx","file_size":12385,"timestamp":"2025-08-05 06:11:57"} 
[2025-08-05 06:11:57] local.INFO: Golongan Import Results {"user_id":1,"results":{"success":0,"failed":58,"errors":[{"row":{"template_import_golongan_lomba":"Petunjuk Penggunaan:","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"1. Isi data mulai dari baris 10 (setelah header)","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"2. Kolom cabang_lomba: Harus sesuai dengan nama cabang yang sudah ada","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"3. Kolom jenis_kelamin: \"Laki-laki\" atau \"Perempuan\"","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"4. Kolom batas_umur_min/max: Angka dalam tahun","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"5. Kolom biaya_pendaftaran: Angka tanpa titik/koma","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"6. Kolom nomor_urut: Boleh kosong, akan diisi otomatis","1":null,"2":null,"3":null,"4":null,"5":null,"6":null,"7":null,"8":null,"9":null,"10":null},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"kode_golongan","1":"nama_golongan","2":"cabang_lomba","3":"jenis_kelamin","4":"batas_umur_min","5":"batas_umur_max","6":"kuota_maksimal","7":"biaya_pendaftaran","8":"nomor_urut_awal","9":"nomor_urut_akhir","10":"status"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ2A","1":"Anak-anak Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":14,"6":50,"7":1,"8":30,"9":50,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ2B","1":"Anak-anak Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":14,"6":50,"7":1,"8":30,"9":100,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ4A","1":"Cacat Netra Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":49,"6":30,"7":211,"8":240,"9":130,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ4B","1":"Cacat Netra Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":49,"6":50,"7":211,"8":240,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ5A","1":"Dewasa Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":40,"6":50,"7":61,"8":90,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ6A","1":"Qira'at Mujawad Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":40,"6":50,"7":181,"8":210,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ6B","1":"Qira'at Mujawad Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":40,"6":50,"7":181,"8":210,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ7A","1":"Qira'at Murattal Dewasa Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":40,"6":50,"7":151,"8":180,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ7B","1":"Qira'at Murattal Dewasa Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":40,"6":50,"7":151,"8":180,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ1A","1":"1 Juz Tilawah Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":16,"6":50,"7":241,"8":270,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ1B","1":"1 Juz Tilawah Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":15,"6":50,"7":241,"8":270,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ2A","1":"5 Juz dan Tilawah Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":20,"6":50,"7":271,"8":300,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ2B","1":"5 Juz dan Tilawah Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":20,"6":50,"7":271,"8":300,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ3A","1":"10 Juz Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":301,"8":330,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ3B","1":"10 Juz Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":301,"8":330,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ4A","1":"20 Juz Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":331,"8":360,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ4B","1":"20 Juz Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":331,"8":360,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ5A","1":"30 Juz Putra","2":"\tHifzh Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":361,"8":390,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HQ5B","1":"30 Juz Putri","2":"\tHifzh Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":361,"8":390,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF1A","1":"Bahasa Arab Putra","2":"Tafsir Al-Quran","3":"Laki-laki","4":6,"5":22,"6":50,"7":421,"8":450,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF1B","1":"Bahasa Arab Putri","2":"Tafsir Al-Quran","3":"Perempuan","4":6,"5":22,"6":50,"7":421,"8":450,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF2A","1":"Bahasa Indonesia Putra","2":"Tafsir Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":391,"8":420,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF2B","1":"Bahasa Indonesia Putri","2":"Tafsir Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":391,"8":420,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"FQ1A","1":"Fahm Al-Quran Putra","2":"Fahm Al-Quran","3":"Laki-laki","4":6,"5":18,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"FQ1B","1":"Fahm Al-Quran Putri","2":"Fahm Al-Quran","3":"Perempuan","4":6,"5":18,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"SQ1A","1":"Syarhil Qur'an Putra","2":"Syarh Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"SQ1B","1":"Syarh Al-Quran Putri","2":"Syarh Al-Quran","3":"Perempuan","4":6,"5":18,"6":50,"7":0,"8":0,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ1A","1":"Naskah Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":481,"8":510,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ1B","1":"Naskah Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":481,"8":510,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ2A","1":"Hiasan Mushaf Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":511,"8":540,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ2B","1":"Hiasan Mushaf Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":511,"8":540,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ3A","1":"Dekorasi Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":541,"8":570,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ3B","1":"Dekorasi Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":541,"8":570,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ4A","1":"Kontemporer Putra","2":"\tKhath Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":571,"8":600,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"KQ4B","1":"Kontemporer Putri","2":"\tKhath Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":571,"8":600,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"MQ1A","1":"Makalah Putra","2":"\tMakalah Ilmiah Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":601,"8":630,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"MQ1B","1":"Makalah Putri","2":"\tMakalah Ilmiah Al-Quran","3":"Perempuan","4":6,"5":24,"6":50,"7":601,"8":630,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ8A","1":"Qiroat Murottal Remaja Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":121,"8":150,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ8B","1":"Qiroat Murottal Remaja Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":24,"6":50,"7":121,"8":150,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ5B","1":"Dewasa Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":40,"6":50,"7":61,"8":90,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF3A","1":"Bahasa Inggris Putra","2":"Tafsir Al-Quran","3":"Laki-laki","4":6,"5":34,"6":50,"7":451,"8":480,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TF3B","1":"Bahasa Inggris Putri","2":"Tafsir Al-Quran","3":"Perempuan","4":6,"5":34,"6":50,"7":451,"8":480,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH1A","1":"Hafalan 100 Hadist Putra","2":"\tHafalan Hadits","3":"Laki-laki","4":6,"5":22,"6":50,"7":631,"8":660,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH1B","1":"Hafalan 100 Hadist Putri","2":"\tHafalan Hadits","3":"Perempuan","4":6,"5":22,"6":50,"7":631,"8":660,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH2A","1":"Hafalan 500 Hadist Putra","2":"\tHafalan Hadits","3":"Laki-laki","4":6,"5":22,"6":50,"7":661,"8":690,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"HH2B","1":"Hafalan 500 Hadist Putri","2":"\tHafalan Hadits","3":"Perempuan","4":6,"5":22,"6":50,"7":661,"8":690,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ3A","1":"Remaja Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":24,"6":50,"7":31,"8":60,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ3B","1":"Remaja Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":24,"6":50,"7":31,"8":60,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ1A","1":"Tartil Putra","2":"Tilawah Al-Quran","3":"Laki-laki","4":6,"5":13,"6":50,"7":91,"8":120,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"},{"row":{"template_import_golongan_lomba":"TQ1B","1":"Tartil Putri","2":"Tilawah Al-Quran","3":"Perempuan","4":6,"5":12,"6":50,"7":91,"8":120,"9":0,"10":"aktif"},"error":"Jenis kelamin wajib diisi"}],"created":[],"updated":[]},"timestamp":"2025-08-05 06:11:57"} 
[2025-08-05 06:13:33] local.INFO: Golongan Import Request {"user_id":1,"file_name":"template-import-golongan.xlsx","file_size":11910,"timestamp":"2025-08-05 06:13:33"} 
[2025-08-05 06:13:33] local.INFO: Golongan Import Results {"user_id":1,"results":{"success":46,"failed":4,"errors":[{"row":{"kode_golongan":"FQ1A","nama_golongan":"Fahm Al-Quran Putra","cabang_lomba":"Fahm Al-Quran","jenis_kelamin":"Laki-laki","batas_umur_min":6,"batas_umur_max":18,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (FQ1A, Fahm Al-Quran Putra, L, 6, 18, 50, 0, ?, ?, aktif, 4, 2025-08-05 06:13:33, 2025-08-05 06:13:33))"},{"row":{"kode_golongan":"FQ1B","nama_golongan":"Fahm Al-Quran Putri","cabang_lomba":"Fahm Al-Quran","jenis_kelamin":"Perempuan","batas_umur_min":6,"batas_umur_max":18,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (FQ1B, Fahm Al-Quran Putri, P, 6, 18, 50, 0, ?, ?, aktif, 4, 2025-08-05 06:13:33, 2025-08-05 06:13:33))"},{"row":{"kode_golongan":"SQ1A","nama_golongan":"Syarhil Qur'an Putra","cabang_lomba":"Syarh Al-Quran","jenis_kelamin":"Laki-laki","batas_umur_min":6,"batas_umur_max":24,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (SQ1A, Syarhil Qur'an Putra, L, 6, 24, 50, 0, ?, ?, aktif, 5, 2025-08-05 06:13:33, 2025-08-05 06:13:33))"},{"row":{"kode_golongan":"SQ1B","nama_golongan":"Syarh Al-Quran Putri","cabang_lomba":"Syarh Al-Quran","jenis_kelamin":"Perempuan","batas_umur_min":6,"batas_umur_max":18,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (SQ1B, Syarh Al-Quran Putri, P, 6, 18, 50, 0, ?, ?, aktif, 5, 2025-08-05 06:13:33, 2025-08-05 06:13:33))"}],"created":["TQ2A","TQ2B","TQ4A","TQ4B","TQ5A","TQ6A","TQ6B","TQ7A","TQ7B","HQ1A","HQ1B","HQ2A","HQ2B","HQ3A","HQ3B","HQ4A","HQ4B","HQ5A","HQ5B","TF1A","TF1B","TF2A","TF2B","KQ1A","KQ1B","KQ2A","KQ2B","KQ3A","KQ3B","KQ4A","KQ4B","MQ1A","MQ1B","TQ8A","TQ8B","TQ5B","TF3A","TF3B","HH1A","HH1B","HH2A","HH2B","TQ3A","TQ3B","TQ1A","TQ1B"],"updated":[]},"timestamp":"2025-08-05 06:13:33"} 
[2025-08-05 06:13:34] local.INFO: Golongan Import Request {"user_id":1,"file_name":"template-import-golongan.xlsx","file_size":11910,"timestamp":"2025-08-05 06:13:34"} 
[2025-08-05 06:13:34] local.INFO: Golongan Import Results {"user_id":1,"results":{"success":46,"failed":4,"errors":[{"row":{"kode_golongan":"FQ1A","nama_golongan":"Fahm Al-Quran Putra","cabang_lomba":"Fahm Al-Quran","jenis_kelamin":"Laki-laki","batas_umur_min":6,"batas_umur_max":18,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (FQ1A, Fahm Al-Quran Putra, L, 6, 18, 50, 0, ?, ?, aktif, 4, 2025-08-05 06:13:34, 2025-08-05 06:13:34))"},{"row":{"kode_golongan":"FQ1B","nama_golongan":"Fahm Al-Quran Putri","cabang_lomba":"Fahm Al-Quran","jenis_kelamin":"Perempuan","batas_umur_min":6,"batas_umur_max":18,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (FQ1B, Fahm Al-Quran Putri, P, 6, 18, 50, 0, ?, ?, aktif, 4, 2025-08-05 06:13:34, 2025-08-05 06:13:34))"},{"row":{"kode_golongan":"SQ1A","nama_golongan":"Syarhil Qur'an Putra","cabang_lomba":"Syarh Al-Quran","jenis_kelamin":"Laki-laki","batas_umur_min":6,"batas_umur_max":24,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (SQ1A, Syarhil Qur'an Putra, L, 6, 24, 50, 0, ?, ?, aktif, 5, 2025-08-05 06:13:34, 2025-08-05 06:13:34))"},{"row":{"kode_golongan":"SQ1B","nama_golongan":"Syarh Al-Quran Putri","cabang_lomba":"Syarh Al-Quran","jenis_kelamin":"Perempuan","batas_umur_min":6,"batas_umur_max":18,"kuota_maksimal":50,"biaya_pendaftaran":0,"nomor_urut_awal":0,"nomor_urut_akhir":0,"status":"aktif"},"error":"SQLSTATE[23000]: Integrity constraint violation: 19 NOT NULL constraint failed: golongan.nomor_urut_awal (Connection: sqlite, SQL: insert into \"golongan\" (\"kode_golongan\", \"nama_golongan\", \"jenis_kelamin\", \"batas_umur_min\", \"batas_umur_max\", \"kuota_max\", \"biaya_pendaftaran\", \"nomor_urut_awal\", \"nomor_urut_akhir\", \"status\", \"id_cabang\", \"updated_at\", \"created_at\") values (SQ1B, Syarh Al-Quran Putri, P, 6, 18, 50, 0, ?, ?, aktif, 5, 2025-08-05 06:13:34, 2025-08-05 06:13:34))"}],"created":[],"updated":["TQ2A","TQ2B","TQ4A","TQ4B","TQ5A","TQ6A","TQ6B","TQ7A","TQ7B","HQ1A","HQ1B","HQ2A","HQ2B","HQ3A","HQ3B","HQ4A","HQ4B","HQ5A","HQ5B","TF1A","TF1B","TF2A","TF2B","KQ1A","KQ1B","KQ2A","KQ2B","KQ3A","KQ3B","KQ4A","KQ4B","MQ1A","MQ1B","TQ8A","TQ8B","TQ5B","TF3A","TF3B","HH1A","HH1B","HH2A","HH2B","TQ3A","TQ3B","TQ1A","TQ1B"]},"timestamp":"2025-08-05 06:13:34"} 
[2025-08-05 06:17:26] local.INFO: Golongan Import Request {"user_id":1,"file_name":"template-import-golongan.xlsx","file_size":12005,"timestamp":"2025-08-05 06:17:26"} 
[2025-08-05 06:17:26] local.INFO: Golongan Import Results {"user_id":1,"results":{"success":50,"failed":0,"errors":[],"created":["FQ1A","FQ1B","SQ1A","SQ1B","TQ2A","TQ2B","TQ3A","TQ3B","TQ5A","TQ5B","TQ1A","TQ1B","TQ8A","TQ8B","TQ7A","TQ7B","TQ6A","TQ6B","TQ4A","TQ4B","HQ1A","HQ1B","HQ2A","HQ2B","HQ3A","HQ3B","HQ4A","HQ4B","HQ5A","HQ5B","TF2A","TF2B","TF1A","TF1B","TF3A","TF3B","KQ1A","KQ1B","KQ2A","KQ2B","KQ3A","KQ3B","KQ4A","KQ4B","MQ1A","MQ1B","HH1A","HH1B","HH2A","HH2B"],"updated":[]},"timestamp":"2025-08-05 06:17:26"} 
[2025-08-05 06:17:26] local.INFO: Golongan Import Request {"user_id":1,"file_name":"template-import-golongan.xlsx","file_size":12005,"timestamp":"2025-08-05 06:17:26"} 
[2025-08-05 06:17:26] local.INFO: Golongan Import Results {"user_id":1,"results":{"success":50,"failed":0,"errors":[],"created":[],"updated":["FQ1A","FQ1B","SQ1A","SQ1B","TQ2A","TQ2B","TQ3A","TQ3B","TQ5A","TQ5B","TQ1A","TQ1B","TQ8A","TQ8B","TQ7A","TQ7B","TQ6A","TQ6B","TQ4A","TQ4B","HQ1A","HQ1B","HQ2A","HQ2B","HQ3A","HQ3B","HQ4A","HQ4B","HQ5A","HQ5B","TF2A","TF2B","TF1A","TF1B","TF3A","TF3B","KQ1A","KQ1B","KQ2A","KQ2B","KQ3A","KQ3B","KQ4A","KQ4B","MQ1A","MQ1B","HH1A","HH1B","HH2A","HH2B"]},"timestamp":"2025-08-05 06:17:26"} 
[2025-08-05 10:29:52] local.INFO: peserta Template Download {"user_id":3,"timestamp":"2025-08-05 10:29:52"} 
[2025-08-05 10:32:18] local.INFO: peserta Template Download {"user_id":3,"timestamp":"2025-08-05 10:32:18"} 
