<template>
  <Dialog :open="show" @update:open="emit('update:show', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon :name="getStatusIcon()" :class="getStatusIconClass()" class="w-5 h-5" />
          {{ title }}
        </DialogTitle>
        <DialogDescription>
          {{ getStatusDescription() }}
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- Progress Bar -->
        <div class="space-y-2">
          <div class="flex justify-between text-sm">
            <span>Progress</span>
            <span>{{ Math.round(progress) }}%</span>
          </div>
          <div class="w-full bg-gray-200 rounded-full h-2">
            <div
              class="h-2 rounded-full transition-all duration-300"
              :class="getProgressBarClass()"
              :style="{ width: `${progress}%` }"
            ></div>
          </div>
        </div>

        <!-- Status Message -->
        <div v-if="status === 'processing'" class="flex items-center gap-2 text-sm text-gray-600">
          <Icon name="loader" class="w-4 h-4 animate-spin" />
          Sedang memproses data...
        </div>

        <!-- Results Summary -->
        <div v-if="status === 'completed' && results" class="space-y-3">
          <div class="bg-green-50 border border-green-200 rounded-lg p-3">
            <div class="flex items-center gap-2">
              <Icon name="check-circle" class="w-5 h-5 text-green-600" />
              <span class="font-medium text-green-900">Import Berhasil</span>
            </div>
            <div class="mt-2 text-sm text-green-700">
              <p v-if="results.success">{{ results.success }}</p>
              <div v-if="results.created && results.created.length > 0" class="mt-1">
                <span class="font-medium">Data baru:</span> {{ results.created.length }} item
              </div>
              <div v-if="results.updated && results.updated.length > 0" class="mt-1">
                <span class="font-medium">Data diperbarui:</span> {{ results.updated.length }} item
              </div>
            </div>
          </div>

          <!-- Warnings -->
          <div v-if="results.warning || (results.import_errors && results.import_errors.length > 0)"
               class="bg-yellow-50 border border-yellow-200 rounded-lg p-3">
            <div class="flex items-center gap-2">
              <Icon name="alert-triangle" class="w-5 h-5 text-yellow-600" />
              <span class="font-medium text-yellow-900">Peringatan</span>
            </div>
            <div class="mt-2 text-sm text-yellow-700">
              <p v-if="results.warning">{{ results.warning }}</p>
              <div v-if="results.import_errors && results.import_errors.length > 0" class="mt-2">
                <details class="cursor-pointer">
                  <summary class="font-medium">Detail Error ({{ results.import_errors.length }} item)</summary>
                  <div class="mt-2 space-y-1 max-h-32 overflow-y-auto">
                    <div v-for="(error, index) in results.import_errors.slice(0, 5)" :key="index"
                         class="text-xs bg-yellow-100 p-2 rounded">
                      <div v-if="error.row">Baris: {{ JSON.stringify(error.row) }}</div>
                      <div class="text-red-600">{{ error.error || error.errors?.join(', ') }}</div>
                    </div>
                    <div v-if="results.import_errors.length > 5" class="text-xs text-yellow-600">
                      ... dan {{ results.import_errors.length - 5 }} error lainnya
                    </div>
                  </div>
                </details>
              </div>
            </div>
          </div>
        </div>

        <!-- Error Message -->
        <div v-if="status === 'error'" class="bg-red-50 border border-red-200 rounded-lg p-3">
          <div class="flex items-center gap-2">
            <Icon name="x-circle" class="w-5 h-5 text-red-600" />
            <span class="font-medium text-red-900">Import Gagal</span>
          </div>
          <div class="mt-2 text-sm text-red-700">
            <p v-if="results?.error">{{ results.error }}</p>
            <p v-else-if="results?.errors">
              {{ Object.values(results.errors).flat().join(', ') }}
            </p>
            <p v-else>Terjadi kesalahan saat import data.</p>
          </div>
        </div>
      </div>

      <DialogFooter>
        <Button
          v-if="status === 'completed' || status === 'error'"
          @click="emit('close')"
        >
          Tutup
        </Button>
        <Button
          v-if="status === 'processing'"
          variant="outline"
          @click="emit('close')"
        >
          Batal
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { computed } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  title: string
  progress: number
  status: 'idle' | 'processing' | 'completed' | 'error'
  results?: any
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
  'close': []
}>()

// Methods
const getStatusIcon = () => {
  const icons = {
    'idle': 'clock',
    'processing': 'loader',
    'completed': 'check-circle',
    'error': 'x-circle'
  }
  return icons[props.status] || 'clock'
}

const getStatusIconClass = () => {
  const classes = {
    'idle': 'text-gray-500',
    'processing': 'text-blue-500 animate-spin',
    'completed': 'text-green-500',
    'error': 'text-red-500'
  }
  return classes[props.status] || 'text-gray-500'
}

const getStatusDescription = () => {
  const descriptions = {
    'idle': 'Menunggu untuk memulai import...',
    'processing': 'Sedang memproses file import...',
    'completed': 'Import data telah selesai.',
    'error': 'Terjadi kesalahan saat import.'
  }
  return descriptions[props.status] || ''
}

const getProgressBarClass = () => {
  const classes = {
    'idle': 'bg-gray-400',
    'processing': 'bg-blue-500',
    'completed': 'bg-green-500',
    'error': 'bg-red-500'
  }
  return classes[props.status] || 'bg-gray-400'
}
</script>
