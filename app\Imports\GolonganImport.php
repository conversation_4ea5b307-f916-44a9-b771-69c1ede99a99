<?php

namespace App\Imports;

use App\Models\Golongan;
use App\Models\CabangLomba;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Validators\Failure;

class GolonganImport implements ToCollection, WithHeadingRow, WithValidation, SkipsOnFailure
{
    use SkipsFailures;

    protected $importResults = [
        'success' => 0,
        'failed' => 0,
        'errors' => [],
        'created' => [],
        'updated' => []
    ];

    protected $cabangLombaCache = [];

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        // Cache cabang lomba for performance
        $this->cabangLombaCache = CabangLomba::pluck('id_cabang', 'nama_cabang')->toArray();

        foreach ($collection as $row) {
            try {
                $this->processRow($row);
            } catch (\Exception $e) {
                $this->importResults['failed']++;
                $this->importResults['errors'][] = [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    protected function processRow($row)
    {
        // Clean and validate data
        $data = [
            'kode_golongan' => trim($row['kode_golongan'] ?? ''),
            'nama_golongan' => trim($row['nama_golongan'] ?? ''),
            'cabang_lomba' => trim($row['cabang_lomba'] ?? ''),
            'jenis_kelamin' => $this->normalizeGender($row['jenis_kelamin'] ?? ''),
            'batas_umur_min' => (int) ($row['batas_umur_min'] ?? 0),
            'batas_umur_max' => (int) ($row['batas_umur_max'] ?? 0),
            'kuota_max' => (int) ($row['kuota_maksimal'] ?? 0),
            'biaya_pendaftaran' => $this->parseAmount($row['biaya_pendaftaran'] ?? 0),
            'nomor_urut_awal' => !empty($row['nomor_urut_awal']) ? (int) $row['nomor_urut_awal'] : null,
            'nomor_urut_akhir' => !empty($row['nomor_urut_akhir']) ? (int) $row['nomor_urut_akhir'] : null,
            'status' => $this->normalizeStatus($row['status'] ?? 'aktif')
        ];

        // Validate required fields
        if (empty($data['kode_golongan']) || empty($data['nama_golongan']) || empty($data['cabang_lomba'])) {
            throw new \Exception('Kode golongan, nama golongan, dan cabang lomba wajib diisi');
        }

        // Find cabang lomba ID
        if (!isset($this->cabangLombaCache[$data['cabang_lomba']])) {
            throw new \Exception("Cabang lomba '{$data['cabang_lomba']}' tidak ditemukan");
        }

        $data['id_cabang'] = $this->cabangLombaCache[$data['cabang_lomba']];
        unset($data['cabang_lomba']);

        // Additional validations
        if ($data['batas_umur_min'] <= 0 || $data['batas_umur_max'] <= 0) {
            throw new \Exception('Batas umur harus lebih dari 0');
        }

        if ($data['batas_umur_min'] > $data['batas_umur_max']) {
            throw new \Exception('Batas umur minimum tidak boleh lebih besar dari maksimum');
        }

        if ($data['kuota_max'] <= 0) {
            throw new \Exception('Kuota maksimal harus lebih dari 0');
        }

        // Check if golongan already exists
        $existingGolongan = Golongan::where('kode_golongan', $data['kode_golongan'])->first();

        if ($existingGolongan) {
            // Update existing
            $existingGolongan->update($data);
            $this->importResults['updated'][] = $data['kode_golongan'];
        } else {
            // Create new
            Golongan::create($data);
            $this->importResults['created'][] = $data['kode_golongan'];
        }

        $this->importResults['success']++;
    }

    protected function normalizeGender($gender)
    {
        $gender = strtolower(trim($gender));
        
        if (in_array($gender, ['l', 'laki-laki', 'male', 'pria'])) {
            return 'L';
        } elseif (in_array($gender, ['p', 'perempuan', 'female', 'wanita'])) {
            return 'P';
        }
        
        throw new \Exception("Jenis kelamin tidak valid: {$gender}");
    }

    protected function normalizeStatus($status)
    {
        $status = strtolower(trim($status));
        
        if (in_array($status, ['aktif', 'active', '1', 'ya', 'yes'])) {
            return 'aktif';
        }
        
        return 'non_aktif';
    }

    protected function parseAmount($amount)
    {
        // Remove currency symbols and formatting
        $amount = preg_replace('/[^\d,.]/', '', $amount);
        $amount = str_replace(',', '', $amount);
        
        return (float) $amount;
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'kode_golongan' => 'required|string|max:10',
            'nama_golongan' => 'required|string|max:100',
            'cabang_lomba' => 'required|string',
            'jenis_kelamin' => 'required|string',
            'batas_umur_min' => 'required|integer|min:1',
            'batas_umur_max' => 'required|integer|min:1',
            'kuota_maksimal' => 'required|integer|min:1',
            'biaya_pendaftaran' => 'required|numeric|min:0'
        ];
    }

    /**
     * Get import results
     */
    public function getImportResults()
    {
        return $this->importResults;
    }

    /**
     * @param Failure[] $failures
     */
    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->importResults['failed']++;
            $this->importResults['errors'][] = [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(),
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ];
        }
    }
}
