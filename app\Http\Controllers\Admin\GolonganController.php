<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Golongan;
use App\Models\CabangLomba;
use App\Exports\GolonganExport;
use App\Exports\GolonganTemplateExport;
use App\Imports\GolonganImport;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class GolonganController extends Controller
{
    /**
     * Display a listing of golongan.
     */
    public function index(Request $request)
    {
        $query = Golongan::with(['cabangLomba'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('nama_golongan', 'like', "%{$search}%")
                      ->orWhere('kode_golongan', 'like', "%{$search}%")
                      ->orWhereHas('cabangLomba', function ($q) use ($search) {
                          $q->where('nama_cabang', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->cabang, function ($query, $cabang) {
                if ($cabang !== 'all') {
                    $query->where('id_cabang', $cabang);
                }
            })
            ->when($request->jenis_kelamin, function ($query, $jenisKelamin) {
                if ($jenisKelamin !== 'all') {
                    $query->where('jenis_kelamin', $jenisKelamin);
                }
            })
            ->when($request->status, function ($query, $status) {
                if ($status !== 'all') {
                    $query->where('status', $status);
                }
            });

        $golongan = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/Golongan/Index', [
            'golongan' => $golongan,
            'filters' => $request->only(['search', 'cabang', 'jenis_kelamin', 'status']),
            'cabangLomba' => CabangLomba::aktif()->get()
        ]);
    }

    /**
     * Show the form for creating a new golongan.
     */
    public function create(Request $request)
    {
        return Inertia::render('Admin/Golongan/Create', [
            'cabangLomba' => CabangLomba::aktif()->get(),
            'selectedCabang' => $request->cabang
        ]);
    }

    /**
     * Store a newly created golongan in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'kode_golongan' => 'required|string|max:10|unique:golongan',
            'nama_golongan' => 'required|string|max:100',
            'id_cabang' => 'required|exists:cabang_lomba,id_cabang',
            'jenis_kelamin' => ['required', Rule::in(['L', 'P'])],
            'batas_umur_min' => 'required|integer|min:1|max:100',
            'batas_umur_max' => 'required|integer|min:1|max:100|gte:batas_umur_min',
            'kuota_max' => 'required|integer|min:1',
            'biaya_pendaftaran' => 'required|numeric|min:0',
            'nomor_urut_awal' => 'nullable|integer|min:1',
            'nomor_urut_akhir' => 'nullable|integer|min:1|gte:nomor_urut_awal',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        // Set default values for nomor urut if not provided
        if (!$validated['nomor_urut_awal']) {
            $validated['nomor_urut_awal'] = 1;
        }
        if (!$validated['nomor_urut_akhir']) {
            $validated['nomor_urut_akhir'] = 999;
        }

        Golongan::create($validated);

        return redirect()->route('admin.golongan.index')
            ->with('success', 'Golongan berhasil dibuat.');
    }

    /**
     * Display the specified golongan.
     */
    public function show(Golongan $golongan)
    {
        $golongan->load(['cabangLomba', 'pendaftaran.peserta']);

        return Inertia::render('Admin/Golongan/Show', [
            'golongan' => $golongan
        ]);
    }

    /**
     * Show the form for editing the specified golongan.
     */
    public function edit(Golongan $golongan)
    {
        return Inertia::render('Admin/Golongan/Edit', [
            'golongan' => $golongan,
            'cabangLomba' => CabangLomba::aktif()->get()
        ]);
    }

    /**
     * Update the specified golongan in storage.
     */
    public function update(Request $request, Golongan $golongan)
    {
        $validated = $request->validate([
            'kode_golongan' => ['required', 'string', 'max:10', Rule::unique('golongan')->ignore($golongan->id_golongan, 'id_golongan')],
            'nama_golongan' => 'required|string|max:100',
            'id_cabang' => 'required|exists:cabang_lomba,id_cabang',
            'jenis_kelamin' => ['required', Rule::in(['L', 'P'])],
            'batas_umur_min' => 'required|integer|min:1|max:100',
            'batas_umur_max' => 'required|integer|min:1|max:100|gte:batas_umur_min',
            'kuota_max' => 'required|integer|min:1',
            'biaya_pendaftaran' => 'required|numeric|min:0',
            'nomor_urut_awal' => 'nullable|integer|min:1',
            'nomor_urut_akhir' => 'nullable|integer|min:1|gte:nomor_urut_awal',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        // Set default values for nomor urut if not provided
        if (!$validated['nomor_urut_awal']) {
            $validated['nomor_urut_awal'] = 1;
        }
        if (!$validated['nomor_urut_akhir']) {
            $validated['nomor_urut_akhir'] = 999;
        }

        $golongan->update($validated);

        return redirect()->route('admin.golongan.index')
            ->with('success', 'Golongan berhasil diperbarui.');
    }

    /**
     * Remove the specified golongan from storage.
     */
    public function destroy(Golongan $golongan)
    {
        // Check if golongan has pendaftaran
        if ($golongan->pendaftaran()->count() > 0) {
            return back()->with('error', 'Golongan tidak dapat dihapus karena memiliki pendaftaran.');
        }

        $golongan->delete();

        return redirect()->route('admin.golongan.index')
            ->with('success', 'Golongan berhasil dihapus.');
    }

    /**
     * Export golongan data to Excel/CSV
     */
    public function export(Request $request)
    {
        $filters = $request->only(['search', 'status', 'id_cabang']);
        $format = $request->get('format', 'xlsx');

        $filename = 'golongan-' . date('Y-m-d-H-i-s') . '.' . $format;

        // Log export activity
        Log::info('Golongan Export', [
            'user_id' => Auth::id(),
            'filters' => $filters,
            'format' => $format,
            'timestamp' => now()
        ]);

        return Excel::download(new GolonganExport($filters), $filename);
    }

    /**
     * Download import template
     */
    public function downloadTemplate()
    {
        $filename = 'template-import-golongan.xlsx';

        Log::info('Golongan Template Download', [
            'user_id' => Auth::id(),
            'timestamp' => now()
        ]);

        return Excel::download(new GolonganTemplateExport(), $filename);
    }

    /**
     * Import golongan data from Excel/CSV
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:5120', // 5MB max
        ]);

        try {
            $import = new GolonganImport();
            Excel::import($import, $request->file('file'));

            $results = $import->getImportResults();

            // Log import activity
            Log::info('Golongan Import', [
                'user_id' => Auth::id(),
                'results' => $results,
                'timestamp' => now()
            ]);

            if ($results['failed'] > 0) {
                return back()->with([
                    'warning' => "Import selesai dengan {$results['success']} berhasil dan {$results['failed']} gagal.",
                    'import_errors' => $results['errors']
                ]);
            }

            return back()->with('success', "Import berhasil! {$results['success']} data golongan telah diproses.");

        } catch (\Exception $e) {
            Log::error('Golongan Import Error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);

            return back()->with('error', 'Terjadi kesalahan saat import: ' . $e->getMessage());
        }
    }
}
