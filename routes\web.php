<?php

use App\Http\Controllers\Admin\DashboardController as AdminDashboardController;
use App\Http\Controllers\Admin\VerificationDashboardController as AdminVerificationDashboardController;
use App\Http\Controllers\Admin\PesertaController;
use App\Http\Controllers\Admin\PendaftaranController;
use App\Http\Controllers\Admin\UserController as AdminUserController;
use App\Http\Controllers\Admin\WilayahController as AdminWilayahController;
use App\Http\Controllers\Admin\CabangLombaController as AdminCabangLombaController;
use App\Http\Controllers\Admin\GolonganController as AdminGolonganController;
use App\Http\Controllers\Admin\MimbarController as AdminMimbarController;
use App\Http\Controllers\Admin\DewaHakimController as AdminDewaHakimController;
use App\Http\Controllers\Admin\PelaksanaanController as AdminPelaksanaanController;
use App\Http\Controllers\Admin\LaporanController as AdminLaporanController;
use App\Http\Controllers\Admin\PembayaranController as AdminPembayaranController;
use App\Http\Controllers\Admin\DokumenVerifikasiController;

use App\Http\Controllers\AdminDaerah\DashboardController as AdminDaerahDashboardController;
use App\Http\Controllers\AdminDaerah\PesertaController as AdminDaerahPesertaController;
use App\Http\Controllers\AdminDaerah\PendaftaranController as AdminDaerahPendaftaranController;
use App\Http\Controllers\AdminDaerah\VerificationController as AdminDaerahVerificationController;
use App\Http\Controllers\AdminDaerah\LaporanController as AdminDaerahLaporanController;

use App\Http\Controllers\CompetitionController;
use App\Http\Controllers\Peserta\DashboardController as PesertaDashboardController;
use App\Http\Controllers\Peserta\PendaftaranController as PesertaPendaftaranController;
use App\Http\Controllers\Peserta\DokumenController;
use App\Http\Controllers\Peserta\ProfileController as PesertaProfileController;
use App\Http\Controllers\Peserta\PembayaranController as PesertaPembayaranController;

use App\Http\Controllers\DewaHakim\DashboardController as DewaHakimDashboardController;
use App\Http\Controllers\DewaHakim\PenilaianController;
use App\Http\Controllers\DewaHakim\ProfileController as DewaHakimProfileController;

use App\Http\Controllers\RedirectController;
use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

Route::get('/', function () {
    $cabangLomba = \App\Models\CabangLomba::with('golongan')->get();
    return Inertia::render('Welcome', [
        'cabangLomba' => $cabangLomba
    ]);
})->name('home');

Route::get('dashboard', function () {
    return Inertia::render('Dashboard');
})->middleware(['auth', 'verified'])->name('dashboard');

// Role-based redirect
Route::get('redirect', [RedirectController::class, 'redirect'])
    ->middleware(['auth', 'verified'])
    ->name('redirect');

// Competition Routes (Public)
Route::prefix('competition')->name('competition.')->group(function () {
    Route::get('/', [CompetitionController::class, 'index'])->name('index');
    Route::get('/cabang/{id}', [CompetitionController::class, 'show'])->name('show');
    Route::get('/golongan/{id}', [CompetitionController::class, 'golongan'])->name('golongan');
});

// Admin Routes (SuperAdmin & Admin)
Route::middleware(['auth', 'role:superadmin,admin'])->prefix('admin')->name('admin.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminDashboardController::class, 'index'])->name('dashboard');

    // Verification Dashboard
    Route::get('/verification-dashboard', [AdminVerificationDashboardController::class, 'index'])->name('verification-dashboard.index');
    Route::get('/verification-dashboard/progress/{verificationType}', [AdminVerificationDashboardController::class, 'getVerificationProgress'])->name('verification-dashboard.progress');

    // User Management
    Route::resource('users', AdminUserController::class);
    Route::post('users/{user}/toggle-status', [AdminUserController::class, 'toggleStatus'])->name('users.toggle-status');
    Route::post('users/{user}/reset-password', [AdminUserController::class, 'resetPassword'])->name('users.reset-password');

    // Regional Management
    Route::resource('wilayah', AdminWilayahController::class);
    Route::get('wilayah/{wilayah}/children', [AdminWilayahController::class, 'children'])->name('wilayah.children');

    // Competition Management
    Route::resource('cabang-lomba', AdminCabangLombaController::class);
    Route::get('cabang-lomba/{cabang}/golongan', [AdminCabangLombaController::class, 'golongan'])->name('cabang-lomba.golongan');

    // Cabang Lomba Export/Import
    Route::get('cabang-lomba-export', [AdminCabangLombaController::class, 'export'])->name('cabang-lomba.export');
    Route::get('cabang-lomba-template', [AdminCabangLombaController::class, 'downloadTemplate'])->name('cabang-lomba.template');
    Route::post('cabang-lomba-import', [AdminCabangLombaController::class, 'import'])->name('cabang-lomba.import');

    Route::resource('golongan', AdminGolonganController::class);

    // Golongan Export/Import
    Route::get('golongan-export', [AdminGolonganController::class, 'export'])->name('golongan.export');
    Route::get('golongan-template', [AdminGolonganController::class, 'downloadTemplate'])->name('golongan.template');
    Route::post('golongan-import', [AdminGolonganController::class, 'import'])->name('golongan.import');

    // Venue Management
    Route::resource('mimbar', AdminMimbarController::class);
    Route::post('mimbar/{mimbar}/toggle-status', [AdminMimbarController::class, 'toggleStatus'])->name('mimbar.toggle-status');

    // Judge Management
    Route::resource('dewan-hakim', AdminDewaHakimController::class);
    Route::post('dewan-hakim/{dewaHakim}/toggle-status', [AdminDewaHakimController::class, 'toggleStatus'])->name('dewan-hakim.toggle-status');
    Route::get('dewan-hakim/{dewaHakim}/profile', [AdminDewaHakimController::class, 'profile'])->name('dewan-hakim.profile');

    // Event Management
    Route::resource('pelaksanaan', AdminPelaksanaanController::class);
    Route::post('pelaksanaan/{pelaksanaan}/activate', [AdminPelaksanaanController::class, 'activate'])->name('pelaksanaan.activate');

    // Participant Management
    Route::resource('peserta', PesertaController::class);
    Route::post('peserta/{peserta}/verify', [PesertaController::class, 'verify'])->name('peserta.verify');
    Route::post('peserta/{peserta}/approve', [PesertaController::class, 'approve'])->name('peserta.approve');
    Route::post('peserta/{peserta}/reject', [PesertaController::class, 'reject'])->name('peserta.reject');

    // Registration Management
    Route::resource('pendaftaran', PendaftaranController::class);
    Route::post('pendaftaran/{pendaftaran}/verify', [PendaftaranController::class, 'verify'])->name('pendaftaran.verify');
    Route::post('pendaftaran/{pendaftaran}/approve', [PendaftaranController::class, 'approve'])->name('pendaftaran.approve');
    Route::post('pendaftaran/{pendaftaran}/reject', [PendaftaranController::class, 'reject'])->name('pendaftaran.reject');
    Route::get('pendaftaran/{pendaftaran}/documents', [PendaftaranController::class, 'documents'])->name('pendaftaran.documents');

    // Payment Management - TEMPORARILY DISABLED
    // Route::resource('pembayaran', AdminPembayaranController::class)->only(['index', 'show', 'update']);
    // Route::post('pembayaran/{pembayaran}/verify', [AdminPembayaranController::class, 'verify'])->name('pembayaran.verify');
    // Route::post('pembayaran/{pembayaran}/reject', [AdminPembayaranController::class, 'reject'])->name('pembayaran.reject');

    // Document Verification
    Route::prefix('dokumen-verifikasi')->name('dokumen-verifikasi.')->group(function () {
        Route::get('/', [DokumenVerifikasiController::class, 'index'])->name('index');
        Route::get('/{dokumen}', [DokumenVerifikasiController::class, 'show'])->name('show');
        Route::post('/{dokumen}/verify', [DokumenVerifikasiController::class, 'verify'])->name('verify');
        Route::get('/{dokumen}/download', [DokumenVerifikasiController::class, 'download'])->name('download');
        Route::post('/bulk-verify', [DokumenVerifikasiController::class, 'bulkVerify'])->name('bulk-verify');
    });

    // Registration Verification Management
    Route::prefix('registration-verification')->name('registration-verification.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'index'])->name('index');
        Route::get('/{pendaftaran}', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'show'])->name('show');
        Route::post('/{pendaftaran}/verify', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'verify'])->name('verify');
        Route::post('/bulk-verify', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'bulkVerify'])->name('bulk-verify');
        Route::post('/bulk-verify-by-status', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'bulkVerifyByStatus'])->name('bulk-verify-by-status');
        Route::post('/documents/{dokumen}/verify', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'verifyDocument'])->name('documents.verify');
        Route::post('/documents/bulk-verify', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'bulkVerifyDocuments'])->name('documents.bulk-verify');
        Route::post('/{pendaftaran}/verify-participant-data', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'verifyParticipantData'])->name('verify-participant-data');
        Route::get('/documents/{dokumen}/download', [\App\Http\Controllers\Admin\RegistrationVerificationController::class, 'downloadDocument'])->name('documents.download');
    });

    // Document Types Management (SuperAdmin only)
    Route::resource('document-types', \App\Http\Controllers\Admin\DocumentTypeController::class);
    Route::post('document-types/{documentType}/toggle-status', [\App\Http\Controllers\Admin\DocumentTypeController::class, 'toggleStatus'])->name('document-types.toggle-status');

    // Golongan Document Requirements Management
    Route::prefix('golongan-document-requirements')->name('golongan-document-requirements.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\GolonganDocumentRequirementController::class, 'index'])->name('index');
        Route::get('/{golongan}', [\App\Http\Controllers\Admin\GolonganDocumentRequirementController::class, 'show'])->name('show');
        Route::put('/{golongan}', [\App\Http\Controllers\Admin\GolonganDocumentRequirementController::class, 'update'])->name('update');
        Route::post('/bulk-update', [\App\Http\Controllers\Admin\GolonganDocumentRequirementController::class, 'bulkUpdate'])->name('bulk-update');
    });

    // Verification Types Management (SuperAdmin only)
    Route::resource('verification-types', \App\Http\Controllers\Admin\VerificationTypeController::class);
    Route::post('verification-types/{verificationType}/toggle-status', [\App\Http\Controllers\Admin\VerificationTypeController::class, 'toggleStatus'])->name('verification-types.toggle-status');

    // Participant Verifications Management
    Route::prefix('participant-verifications')->name('participant-verifications.')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\ParticipantVerificationController::class, 'index'])->name('index');
        Route::get('/nik-verification', [\App\Http\Controllers\Admin\ParticipantVerificationController::class, 'nikVerification'])->name('nik-verification');
        Route::get('/stats', [\App\Http\Controllers\Admin\ParticipantVerificationController::class, 'getVerificationStats'])->name('stats');
        Route::get('/{peserta}', [\App\Http\Controllers\Admin\ParticipantVerificationController::class, 'show'])->name('show');
        Route::post('/{peserta}/verify', [\App\Http\Controllers\Admin\ParticipantVerificationController::class, 'verify'])->name('verify');
        Route::post('/bulk-verify', [\App\Http\Controllers\Admin\ParticipantVerificationController::class, 'bulkVerify'])->name('bulk-verify');
    });

    // Reports
    Route::prefix('laporan')->name('laporan.')->group(function () {
        Route::get('/', [AdminLaporanController::class, 'index'])->name('index');
        Route::get('/peserta', [AdminLaporanController::class, 'peserta'])->name('peserta');
        Route::get('/pendaftaran', [AdminLaporanController::class, 'pendaftaran'])->name('pendaftaran');
        Route::get('/pembayaran', [AdminLaporanController::class, 'pembayaran'])->name('pembayaran');
        Route::get('/wilayah', [AdminLaporanController::class, 'wilayah'])->name('wilayah');
        Route::get('/export/peserta', [AdminLaporanController::class, 'exportPeserta'])->name('export.peserta');
        Route::get('/export/pendaftaran', [AdminLaporanController::class, 'exportPendaftaran'])->name('export.pendaftaran');
    });
});

// Admin Daerah Routes (Regional Administrators)
Route::middleware(['auth', 'role:admin_daerah'])->prefix('admin-daerah')->name('admin-daerah.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [AdminDaerahDashboardController::class, 'index'])->name('dashboard');

    // Participant Management (Regional Admin can directly register participants)
    Route::resource('peserta', AdminDaerahPesertaController::class);
    Route::post('peserta/register-direct', [AdminDaerahPesertaController::class, 'registerDirect'])->name('peserta.register-direct');
    Route::post('peserta/{peserta}/submit', [AdminDaerahPesertaController::class, 'submit'])->name('peserta.submit');
    Route::get('peserta/{peserta}/documents', [AdminDaerahPesertaController::class, 'documents'])->name('peserta.documents');
    Route::post('peserta/{peserta}/upload-document', [AdminDaerahPesertaController::class, 'uploadDocument'])->name('peserta.upload-document');

    // Registration Management for Regional Participants
    Route::resource('pendaftaran', AdminDaerahPendaftaranController::class);
    Route::post('pendaftaran/{pendaftaran}/submit', [AdminDaerahPendaftaranController::class, 'submit'])->name('pendaftaran.submit');
    Route::get('pendaftaran/{pendaftaran}/documents', [AdminDaerahPendaftaranController::class, 'documents'])->name('pendaftaran.documents');
    Route::post('pendaftaran/{pendaftaran}/upload-document', [AdminDaerahPendaftaranController::class, 'uploadDocument'])->name('pendaftaran.upload-document');
    Route::delete('pendaftaran/{pendaftaran}/documents/{document}', [AdminDaerahPendaftaranController::class, 'deleteDocument'])->name('pendaftaran.delete-document');
    Route::get('pendaftaran/eligible-golongan', [AdminDaerahPendaftaranController::class, 'getEligibleGolongan'])->name('pendaftaran.eligible-golongan');

    // Regional Verification (NEW: Admin daerah can verify self-registered participants)
    Route::prefix('verification')->name('verification.')->group(function () {
        Route::get('/', [AdminDaerahVerificationController::class, 'index'])->name('index');
        Route::get('/{participant}', [AdminDaerahVerificationController::class, 'show'])->name('show');
        Route::post('/{participant}/verify', [AdminDaerahVerificationController::class, 'verify'])->name('verify');
        Route::post('/bulk-verify', [AdminDaerahVerificationController::class, 'bulkVerify'])->name('bulk-verify');
    });

    // Regional Reports
    Route::prefix('laporan')->name('laporan.')->group(function () {
        Route::get('/', [AdminDaerahLaporanController::class, 'index'])->name('index');
        Route::get('/peserta', [AdminDaerahLaporanController::class, 'peserta'])->name('peserta');
        Route::get('/pendaftaran', [AdminDaerahLaporanController::class, 'pendaftaran'])->name('pendaftaran');
        Route::get('/export/peserta', [AdminDaerahLaporanController::class, 'exportPeserta'])->name('export.peserta');
    });
});

// Peserta Routes (Participants)
Route::middleware(['auth', 'role:peserta'])->prefix('peserta')->name('peserta.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [PesertaDashboardController::class, 'index'])->name('dashboard');

    // Profile Management
    Route::get('/profile', [PesertaProfileController::class, 'show'])->name('profile.show');
    Route::put('/profile', [PesertaProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/upload-photo', [PesertaProfileController::class, 'uploadPhoto'])->name('profile.upload-photo');

    // Registration Management
    Route::resource('pendaftaran', PesertaPendaftaranController::class);
    Route::post('pendaftaran/{pendaftaran}/submit', [PesertaPendaftaranController::class, 'submit'])->name('pendaftaran.submit');
    Route::post('pendaftaran/{pendaftaran}/cancel', [PesertaPendaftaranController::class, 'cancel'])->name('pendaftaran.cancel');

    // Document Management
    Route::prefix('pendaftaran/{pendaftaran}/dokumen')->name('dokumen.')->group(function () {
        Route::get('/', [DokumenController::class, 'index'])->name('index');
        Route::post('/', [DokumenController::class, 'store'])->name('store');
        Route::get('/{dokumen}/download', [DokumenController::class, 'download'])->name('download');
        Route::post('/{dokumen}/replace', [DokumenController::class, 'replace'])->name('replace');
        Route::delete('/{dokumen}', [DokumenController::class, 'destroy'])->name('destroy');
    });

    // Payment Management - TEMPORARILY DISABLED
    // Route::prefix('pembayaran')->name('pembayaran.')->group(function () {
    //     Route::get('/', [PesertaPembayaranController::class, 'index'])->name('index');
    //     Route::get('/{pendaftaran}', [PesertaPembayaranController::class, 'show'])->name('show');
    //     Route::post('/{pendaftaran}/pay', [PesertaPembayaranController::class, 'pay'])->name('pay');
    //     Route::post('/{pembayaran}/upload-proof', [PesertaPembayaranController::class, 'uploadProof'])->name('upload-proof');
    // });
});

// Dewan Hakim Routes (Judges)
Route::middleware(['auth', 'role:dewan_hakim'])->prefix('dewan-hakim')->name('dewan-hakim.')->group(function () {
    // Dashboard
    Route::get('/dashboard', [DewaHakimDashboardController::class, 'index'])->name('dashboard');

    // Profile Management
    Route::get('/profile', [DewaHakimProfileController::class, 'show'])->name('profile.show');
    Route::put('/profile', [DewaHakimProfileController::class, 'update'])->name('profile.update');
    Route::post('/profile/education', [DewaHakimProfileController::class, 'addEducation'])->name('profile.add-education');
    Route::put('/profile/education/{education}', [DewaHakimProfileController::class, 'updateEducation'])->name('profile.update-education');
    Route::delete('/profile/education/{education}', [DewaHakimProfileController::class, 'deleteEducation'])->name('profile.delete-education');
    Route::post('/profile/experience', [DewaHakimProfileController::class, 'addExperience'])->name('profile.add-experience');
    Route::put('/profile/experience/{experience}', [DewaHakimProfileController::class, 'updateExperience'])->name('profile.update-experience');
    Route::delete('/profile/experience/{experience}', [DewaHakimProfileController::class, 'deleteExperience'])->name('profile.delete-experience');
    Route::post('/profile/achievement', [DewaHakimProfileController::class, 'addAchievement'])->name('profile.add-achievement');
    Route::put('/profile/achievement/{achievement}', [DewaHakimProfileController::class, 'updateAchievement'])->name('profile.update-achievement');
    Route::delete('/profile/achievement/{achievement}', [DewaHakimProfileController::class, 'deleteAchievement'])->name('profile.delete-achievement');

    // Scoring/Assessment
    Route::prefix('penilaian')->name('penilaian.')->group(function () {
        Route::get('/', [PenilaianController::class, 'index'])->name('index');
        Route::get('/peserta/{pendaftaran}', [PenilaianController::class, 'show'])->name('show');
        Route::post('/peserta/{pendaftaran}/score', [PenilaianController::class, 'score'])->name('score');
        Route::put('/peserta/{pendaftaran}/update-score', [PenilaianController::class, 'updateScore'])->name('update-score');
        Route::get('/history', [PenilaianController::class, 'history'])->name('history');
    });
});

// Redirect authenticated users based on role
// Route::middleware(['auth'])->get('/redirect', function () {
//     $user = Auth::user();

//     switch ($user->role) {
//         case 'superadmin':
//         case 'admin':
//         case 'admin_daerah':
//             return redirect()->route('admin.dashboard');
//         case 'peserta':
//             return redirect()->route('peserta.dashboard');
//         case 'dewan_hakim':
//             return redirect()->route('dewan-hakim.dashboard');
//         default:
//             return redirect()->route('dashboard');
//     }
// })->name('redirect');

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
