<?php

namespace App\Exports;

use App\Models\Wilayah;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class PesertaTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    protected $userRole;
    protected $userWilayah;

    public function __construct($userRole = null, $userWilayah = null)
    {
        $this->userRole = $userRole;
        $this->userWilayah = $userWilayah;
    }

    /**
     * @return array
     */
    public function array(): array
    {
        // Get sample wilayah for examples
        $wilayahList = [];
        if ($this->userRole === 'admin_daerah' && $this->userWilayah) {
            $wilayah = Wilayah::find($this->userWilayah);
            $wilayahList[] = $wilayah->nama_wilayah ?? 'Wilayah Admin';
        } else {
            $wilayahList = Wilayah::limit(3)->pluck('nama_wilayah')->toArray();
        }

        return [
            [
                '1234567890123456',
                'Ahmad Fauzi',
                '<EMAIL>',
                'ahmad_fauzi',
                'Jakarta',
                '15/08/1990',
                'Laki-laki',
                'Jl. Merdeka No. 123, Jakarta Pusat',
                $wilayahList[0] ?? 'DKI Jakarta',
                '081234567890',
                'Budi Santoso',
                'Siti Aminah',
                'Guru',
                'SDN 01 Jakarta',
                'self',
                'draft',
                'Ahmad Fauzi',
                '1234567890'
            ],
            [
                '9876543210987654',
                'Siti Nurhaliza',
                '<EMAIL>',
                'siti_nurhaliza',
                'Bandung',
                '20/12/1995',
                'Perempuan',
                'Jl. Asia Afrika No. 456, Bandung',
                $wilayahList[1] ?? 'Jawa Barat',
                '087654321098',
                'Andi Wijaya',
                'Fatimah',
                'Mahasiswa',
                'Universitas Padjadjaran',
                'self',
                'submitted',
                'Siti Nurhaliza',
                '9876543210'
            ],
            [
                '5555666677778888',
                'Muhammad Rizki',
                '<EMAIL>',
                'muhammad_rizki',
                'Surabaya',
                '10/03/1988',
                'Laki-laki',
                'Jl. Pemuda No. 789, Surabaya',
                $wilayahList[2] ?? 'Jawa Timur',
                '085555666677',
                'Hasan Ali',
                'Khadijah',
                'Pegawai Swasta',
                'PT. Maju Jaya',
                'admin_daerah',
                'approved',
                'Muhammad Rizki',
                '5555666677'
            ]
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'nik',
            'nama_lengkap',
            'email',
            'username',
            'tempat_lahir',
            'tanggal_lahir',
            'jenis_kelamin',
            'alamat',
            'wilayah',
            'no_telepon',
            'nama_ayah',
            'nama_ibu',
            'pekerjaan',
            'instansi_asal',
            'registration_type',
            'status_peserta',
            'nama_rekening',
            'no_rekening'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        // Add instructions in the first few rows
        $sheet->insertNewRowBefore(1, 12);
        
        $sheet->setCellValue('A1', 'TEMPLATE IMPORT PESERTA MTQ');
        $sheet->setCellValue('A2', 'Petunjuk Penggunaan:');
        $sheet->setCellValue('A3', '1. Isi data mulai dari baris 14 (setelah header)');
        $sheet->setCellValue('A4', '2. Kolom nik: 16 digit, harus unik');
        $sheet->setCellValue('A5', '3. Kolom email: Format email yang valid, harus unik');
        $sheet->setCellValue('A6', '4. Kolom username: Maksimal 50 karakter, harus unik');
        $sheet->setCellValue('A7', '5. Kolom tanggal_lahir: Format DD/MM/YYYY');
        $sheet->setCellValue('A8', '6. Kolom jenis_kelamin: "Laki-laki" atau "Perempuan"');
        $sheet->setCellValue('A9', '7. Kolom wilayah: Harus sesuai dengan nama wilayah yang ada');
        $sheet->setCellValue('A10', '8. Kolom registration_type: "self", "admin", atau "admin_daerah"');
        $sheet->setCellValue('A11', '9. Kolom status_peserta: "draft", "submitted", "verified", "approved", atau "rejected"');
        $sheet->setCellValue('A12', '10. Kolom yang kosong akan diisi dengan nilai default');

        return [
            // Title style
            1 => [
                'font' => ['bold' => true, 'size' => 14],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            // Instructions style
            '2:12' => [
                'font' => ['italic' => true, 'size' => 10],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFFEF3C7']
                ]
            ],
            // Header style
            13 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }
}
