<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\CabangLomba;
use App\Exports\CabangLombaExport;
use App\Exports\CabangLombaTemplateExport;
use App\Imports\CabangLombaImport;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;

class CabangLombaController extends Controller
{
    /**
     * Display a listing of cabang lomba.
     */
    public function index(Request $request)
    {
        $query = CabangLomba::withCount('golongan')
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('nama_cabang', 'like', "%{$search}%")
                      ->orWhere('kode_cabang', 'like', "%{$search}%");
                });
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            });

        $cabangLomba = $query->latest()->paginate(15)->withQueryString();

        return Inertia::render('Admin/CabangLomba/Index', [
            'cabangLomba' => $cabangLomba,
            'filters' => $request->only(['search', 'status'])
        ]);
    }

    /**
     * Show the form for creating a new cabang lomba.
     */
    public function create()
    {
        return Inertia::render('Admin/CabangLomba/Create');
    }

    /**
     * Store a newly created cabang lomba in storage.
     */
    public function store(Request $request)
    {
        $validated = $request->validate([
            'kode_cabang' => 'required|string|max:10|unique:cabang_lomba',
            'nama_cabang' => 'required|string|max:100',
            'deskripsi' => 'nullable|string',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        CabangLomba::create($validated);

        return redirect()->route('admin.cabang-lomba.index')
            ->with('success', 'Cabang lomba berhasil dibuat.');
    }

    /**
     * Display the specified cabang lomba.
     */
    public function show(CabangLomba $cabangLomba)
    {
        $cabangLomba->load(['golongan']);

        return Inertia::render('Admin/CabangLomba/Show', [
            'cabangLomba' => $cabangLomba
        ]);
    }

    /**
     * Show the form for editing the specified cabang lomba.
     */
    public function edit(CabangLomba $cabangLomba)
    {
        return Inertia::render('Admin/CabangLomba/Edit', [
            'cabangLomba' => $cabangLomba
        ]);
    }

    /**
     * Update the specified cabang lomba in storage.
     */
    public function update(Request $request, CabangLomba $cabangLomba)
    {
        $validated = $request->validate([
            'kode_cabang' => ['required', 'string', 'max:10', Rule::unique('cabang_lomba')->ignore($cabangLomba->id_cabang, 'id_cabang')],
            'nama_cabang' => 'required|string|max:100',
            'deskripsi' => 'nullable|string',
            'status' => ['required', Rule::in(['aktif', 'non_aktif'])]
        ]);

        $cabangLomba->update($validated);

        return redirect()->route('admin.cabang-lomba.index')
            ->with('success', 'Cabang lomba berhasil diperbarui.');
    }

    /**
     * Remove the specified cabang lomba from storage.
     */
    public function destroy(CabangLomba $cabangLomba)
    {
        // Check if cabang lomba has golongan
        if ($cabangLomba->golongan()->count() > 0) {
            return back()->with('error', 'Cabang lomba tidak dapat dihapus karena memiliki golongan.');
        }

        $cabangLomba->delete();

        return redirect()->route('admin.cabang-lomba.index')
            ->with('success', 'Cabang lomba berhasil dihapus.');
    }

    /**
     * Get golongan for specified cabang lomba.
     */
    public function golongan(CabangLomba $cabang)
    {
        $golongan = $cabang->golongan()->aktif()->get();

        return response()->json($golongan);
    }

    /**
     * Export cabang lomba data to Excel/CSV
     */
    public function export(Request $request)
    {
        $filters = $request->only(['search', 'status']);
        $format = $request->get('format', 'xlsx');

        $filename = 'cabang-lomba-' . date('Y-m-d-H-i-s') . '.' . $format;

        // Log export activity
        Log::info('Cabang Lomba Export', [
            'user_id' => Auth::id(),
            'filters' => $filters,
            'format' => $format,
            'timestamp' => now()
        ]);

        return Excel::download(new CabangLombaExport($filters), $filename);
    }

    /**
     * Download import template
     */
    public function downloadTemplate()
    {
        $filename = 'template-import-cabang-lomba.xlsx';

        Log::info('Cabang Lomba Template Download', [
            'user_id' => Auth::id(),
            'timestamp' => now()
        ]);

        return Excel::download(new CabangLombaTemplateExport(), $filename);
    }

    /**
     * Import cabang lomba data from Excel/CSV
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:5120', // 5MB max
        ]);

        try {
            $import = new CabangLombaImport();
            Excel::import($import, $request->file('file'));

            $results = $import->getImportResults();

            // Log import activity
            Log::info('Cabang Lomba Import', [
                'user_id' => Auth::id(),
                'results' => $results,
                'timestamp' => now()
            ]);

            if ($results['failed'] > 0) {
                return back()->with([
                    'warning' => "Import selesai dengan {$results['success']} berhasil dan {$results['failed']} gagal.",
                    'import_errors' => $results['errors']
                ]);
            }

            return back()->with('success', "Import berhasil! {$results['success']} data cabang lomba telah diproses.");

        } catch (\Exception $e) {
            Log::error('Cabang Lomba Import Error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'timestamp' => now()
            ]);

            return back()->with('error', 'Terjadi kesalahan saat import: ' . $e->getMessage());
        }
    }
}
