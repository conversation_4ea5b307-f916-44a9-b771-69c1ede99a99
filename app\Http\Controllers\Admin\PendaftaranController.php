<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\Mimbar;
use App\Exports\PendaftaranExport;
use App\Exports\PendaftaranTemplateExport;
use App\Imports\PendaftaranImport;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Maatwebsite\Excel\Facades\Excel;

/**
 * PendaftaranController
 * handle pendaftaran dari admin daerah maupun peserta
 */

class PendaftaranController extends Controller
{
    public function index(Request $request)
    {
        $query = Pendaftaran::with(['peserta', 'golongan.cabangLomba']);

        // Search functionality
        if ($request->has('search') && $request->search) {
            $search = $request->search;
            $query->where(function ($q) use ($search) {
                $q->whereHas('peserta', function ($q) use ($search) {
                    $q->where('nama_lengkap', 'like', "%{$search}%");
                })
                ->orWhere('nomor_pendaftaran', 'like', "%{$search}%");
            });
        }

        // Filter by status
        if ($request->has('status') && $request->status !== 'all') {
            $query->where('status_pendaftaran', $request->status);
        }

        // Filter by golongan
        if ($request->has('golongan') && $request->golongan !== 'all') {
            $query->where('id_golongan', $request->golongan);
        }

        $pendaftaran = $query->orderBy('created_at', 'desc')
            ->paginate(10)
            ->withQueryString();

        $golongan = Golongan::aktif()->get();

        return Inertia::render('Admin/Pendaftaran/Index', [
            'pendaftaran' => $pendaftaran,
            'golongan' => $golongan,
            'filters' => $request->only(['search', 'status', 'golongan'])
        ]);
    }

    public function show(string $id)
    {
        $pendaftaran = Pendaftaran::with(['peserta', 'golongan.cabangLomba', 'mimbar', 'dokumenPeserta', 'pembayaran'])->find($id);

        return Inertia::render('Admin/Pendaftaran/Show', [
            'pendaftaran' => $pendaftaran
        ]);
    }

    public function create()
    {
        $peserta = Peserta::all();
        $golongan = Golongan::aktif()->get();
        $mimbar = Mimbar::all();

        return Inertia::render('Admin/Pendaftaran/Create', [
            'peserta' => $peserta,
            'golongan' => $golongan,
            'mimbar' => $mimbar
        ]);
    }

    /**
     * Export pendaftaran data to Excel/CSV
     */
    public function export(Request $request)
    {
        $filters = $request->only(['search', 'status', 'golongan', 'tahun', 'regional_verification_status']);
        $format = $request->get('format', 'xlsx');

        $filename = 'pendaftaran-' . date('Y-m-d-H-i-s') . '.' . $format;

        // Log export activity
        Log::info('Pendaftaran Export', [
            'user_id' => Auth::id(),
            'user_role' => Auth::user()->role,
            'filters' => $filters,
            'format' => $format,
            'timestamp' => now()
        ]);

        return Excel::download(new PendaftaranExport($filters, Auth::user()->role), $filename);
    }

    /**
     * Download import template
     */
    public function downloadTemplate()
    {
        $filename = 'template-import-pendaftaran.xlsx';

        Log::info('Pendaftaran Template Download', [
            'user_id' => Auth::id(),
            'user_role' => Auth::user()->role,
            'timestamp' => now()
        ]);

        return Excel::download(new PendaftaranTemplateExport(Auth::user()->role), $filename);
    }

    /**
     * Import pendaftaran data from Excel/CSV
     */
    public function import(Request $request)
    {
        $request->validate([
            'file' => 'required|file|mimes:xlsx,xls,csv|max:5120', // 5MB max
        ]);

        try {
            $import = new PendaftaranImport(Auth::user()->role);
            Excel::import($import, $request->file('file'));

            $results = $import->getImportResults();

            // Log import activity
            Log::info('Pendaftaran Import Results', [
                'user_id' => Auth::id(),
                'user_role' => Auth::user()->role,
                'results' => $results,
                'timestamp' => now()
            ]);

            if ($results['failed'] > 0) {
                return back()->with([
                    'warning' => "Import selesai dengan {$results['success']} berhasil dan {$results['failed']} gagal.",
                    'import_errors' => $results['errors']
                ]);
            }

            return back()->with('success', "Import berhasil! {$results['success']} data pendaftaran telah diproses.");

        } catch (\Maatwebsite\Excel\Validators\ValidationException $e) {
            $failures = $e->failures();
            $errors = [];

            foreach ($failures as $failure) {
                $errors[] = [
                    'row' => $failure->row(),
                    'attribute' => $failure->attribute(),
                    'errors' => $failure->errors(),
                ];
            }

            Log::error('Pendaftaran Import Validation Error', [
                'user_id' => Auth::id(),
                'errors' => $errors,
                'timestamp' => now()
            ]);

            return back()->with([
                'error' => 'Terjadi kesalahan validasi saat import.',
                'import_errors' => $errors
            ]);

        } catch (\Exception $e) {
            Log::error('Pendaftaran Import Error', [
                'user_id' => Auth::id(),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
                'timestamp' => now()
            ]);

            return back()->with('error', 'Terjadi kesalahan saat import: ' . $e->getMessage());
        }
    }

    /**
     * Debug import functionality
     */
    public function importDebug(Request $request)
    {
        // Log everything for debugging
        Log::info('Pendaftaran Import Debug - Request Details', [
            'user_id' => Auth::id(),
            'user_role' => Auth::user()->role,
            'request_method' => $request->method(),
            'request_url' => $request->url(),
            'has_file' => $request->hasFile('file'),
            'file_details' => $request->file('file') ? [
                'name' => $request->file('file')->getClientOriginalName(),
                'size' => $request->file('file')->getSize(),
                'type' => $request->file('file')->getMimeType(),
                'extension' => $request->file('file')->getClientOriginalExtension(),
            ] : null,
            'timestamp' => now()
        ]);

        // Test basic validation
        try {
            $request->validate([
                'file' => 'required|file|mimes:xlsx,xls,csv|max:5120',
            ]);

            Log::info('Pendaftaran Import Debug - Validation passed');

            return response()->json([
                'success' => true,
                'message' => 'Debug test passed - file validation successful',
                'file_info' => [
                    'name' => $request->file('file')->getClientOriginalName(),
                    'size' => $request->file('file')->getSize(),
                    'type' => $request->file('file')->getMimeType(),
                ]
            ]);

        } catch (\Exception $e) {
            Log::error('Pendaftaran Import Debug - Validation failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            return response()->json([
                'success' => false,
                'message' => 'Debug test failed: ' . $e->getMessage()
            ], 422);
        }
    }
}
