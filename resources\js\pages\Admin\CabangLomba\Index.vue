<template>
  <AppLayout :breadcrumbs="breadcrumbItems">
    <Head title="Manajemen Cabang Lomba" />
    <Heading title="Manajemen Cabang Lomba" />

    <div class="space-y-6">
      <!-- Filters and Search -->
      <Card>
        <CardContent class="p-6">
          <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <Label for="search">Pencarian</Label>
              <Input
                id="search"
                v-model="filters.search"
                placeholder="Nama cabang, kode..."
                @input="search"
              />
            </div>
            <div>
              <Label for="status">Status</Label>
              <Select v-model="filters.status" @update:modelValue="search">
                <SelectTrigger>
                  <SelectValue placeholder="Semua Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  <SelectItem value="aktif">Aktif</SelectItem>
                  <SelectItem value="non_aktif">Non Aktif</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div class="flex items-end">
              <Button @click="clearFilters" variant="outline" class="w-full">
                <Icon name="x" class="w-4 h-4 mr-2" />
                Clear
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      <!-- Actions -->
      <div class="flex justify-between items-center">
        <div class="text-sm text-gray-600">
          Menampilkan {{ cabangLomba.from }} - {{ cabangLomba.to }} dari {{ cabangLomba.total }} cabang lomba
        </div>
        <div class="flex gap-2">
          <!-- Export/Import Buttons -->
          <DropdownMenu>
            <DropdownMenuTrigger as-child>
              <Button variant="outline">
                <Icon name="download" class="w-4 h-4 mr-2" />
                Export/Import
                <Icon name="chevron-down" class="w-4 h-4 ml-2" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem @click="showExportModal = true">
                <Icon name="download" class="w-4 h-4 mr-2" />
                Export Data
              </DropdownMenuItem>
              <DropdownMenuItem @click="showImportModal = true">
                <Icon name="upload" class="w-4 h-4 mr-2" />
                Import Data
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>

          <Button @click="$inertia.visit(route('admin.cabang-lomba.create'))">
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Tambah Cabang Lomba
          </Button>
        </div>
      </div>

      <!-- Cabang Lomba Table -->
      <Card>
        <CardContent class="p-0">
          <div class="overflow-x-auto">
            <table class="w-full">
              <thead class="bg-gray-50 border-b">
                <tr>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Cabang Lomba
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Deskripsi
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Jumlah Golongan
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Status
                  </th>
                  <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Dibuat
                  </th>
                  <th class="px-6 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">
                    Aksi
                  </th>
                </tr>
              </thead>
              <tbody class="bg-white divide-y divide-gray-200">
                <tr v-for="item in cabangLomba.data" :key="item.id_cabang" class="hover:bg-gray-50">
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div>
                      <div class="text-sm font-medium text-gray-900">{{ item.nama_cabang }}</div>
                      <div class="text-sm text-gray-500">{{ item.kode_cabang }}</div>
                    </div>
                  </td>
                  <td class="px-6 py-4">
                    <div class="text-sm text-gray-900 max-w-xs truncate">
                      {{ item.deskripsi || '-' }}
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <div class="flex items-center">
                      <Badge variant="outline">
                        {{ item.golongan_count || 0 }} Golongan
                      </Badge>
                    </div>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap">
                    <Badge :variant="getStatusVariant(item.status)">
                      {{ getStatusLabel(item.status) }}
                    </Badge>
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                    {{ formatDate(item.created_at) }}
                  </td>
                  <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.cabang-lomba.show', item.id_cabang))"
                    >
                      <Icon name="eye" class="w-4 h-4" />
                    </Button>
                    <Button
                      variant="ghost"
                      size="sm"
                      @click="$inertia.visit(route('admin.cabang-lomba.edit', item.id_cabang))"
                    >
                      <Icon name="edit" class="w-4 h-4" />
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger as-child>
                        <Button variant="ghost" size="sm">
                          <Icon name="more-vertical" class="w-4 h-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem @click="viewGolongan(item)">
                          <Icon name="list" class="w-4 h-4 mr-2" />
                          Lihat Golongan
                        </DropdownMenuItem>
                        <DropdownMenuItem
                          @click="deleteCabangLomba(item)"
                          class="text-red-600"
                        >
                          <Icon name="trash" class="w-4 h-4 mr-2" />
                          Hapus
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
        </CardContent>
      </Card>

      <!-- Pagination -->
      <Pagination :links="cabangLomba.links" />
    </div>

    <!-- Golongan Dialog -->
    <Dialog v-model:open="golonganDialog.show">
      <DialogContent class="max-w-3xl">
        <DialogHeader>
          <DialogTitle>Golongan: {{ golonganDialog.cabang?.nama_cabang }}</DialogTitle>
          <DialogDescription>
            Daftar golongan yang tersedia untuk cabang lomba {{ golonganDialog.cabang?.nama_cabang }}
          </DialogDescription>
        </DialogHeader>
        <div v-if="golonganDialog.loading" class="flex justify-center py-8">
          <Icon name="loader-2" class="w-6 h-6 animate-spin" />
        </div>
        <div v-else-if="golonganDialog.golongan.length === 0" class="text-center py-8 text-gray-500">
          Belum ada golongan untuk cabang lomba ini
        </div>
        <div v-else class="space-y-3 max-h-96 overflow-y-auto">
          <div
            v-for="golongan in golonganDialog.golongan"
            :key="golongan.id_golongan"
            class="flex justify-between items-center p-4 border rounded-lg"
          >
            <div class="flex-1">
              <div class="font-medium">{{ golongan.nama_golongan }}</div>
              <div class="text-sm text-gray-500">{{ golongan.kode_golongan }}</div>
              <div class="text-sm text-gray-500 mt-1">
                {{ golongan.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }} •
                Usia {{ golongan.batas_umur_min }}-{{ golongan.batas_umur_max }} tahun
              </div>
            </div>
            <div class="flex flex-col items-end gap-2">
              <Badge :variant="getStatusVariant(golongan.status)">
                {{ getStatusLabel(golongan.status) }}
              </Badge>
              <div class="text-sm text-gray-500">
                Kuota: {{ golongan.kuota_max }}
              </div>
              <div class="text-sm font-medium text-green-600">
                Rp {{ formatCurrency(golongan.biaya_pendaftaran) }}
              </div>
            </div>
          </div>
        </div>
        <DialogFooter>
          <Button @click="golonganDialog.show = false">Tutup</Button>
          <Button @click="$inertia.visit(route('admin.golongan.create', { cabang: golonganDialog.cabang?.id_cabang }))">
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Tambah Golongan
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>

    <!-- Export Modal -->
    <ExportModal
      v-model:show="showExportModal"
      title="Cabang Lomba"
      :export-url="route('admin.cabang-lomba.export')"
      :show-filters="true"
    />

    <!-- Import Modal -->
    <ImportModal
      v-model:show="showImportModal"
      title="Cabang Lomba"
      :import-url="route('admin.cabang-lomba.import')"
      :template-url="route('admin.cabang-lomba.template')"
      @imported="handleImportSuccess"
    />
  </AppLayout>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { Head, router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu'
import Icon from '@/components/Icon.vue'
import Pagination from '@/components/Pagination.vue'
import ExportModal from '@/components/ExportModal.vue'
import ImportModal from '@/components/ImportModal.vue'
import { debounce } from 'lodash'
import { type BreadcrumbItem } from '@/types'

const breadcrumbItems: BreadcrumbItem[] = [
  { title: 'Dashboard', href: '/admin/dashboard' },
  { title: 'Manajemen Cabang Lomba', href: '/admin/cabang-lomba' }
]

interface CabangLomba {
  id_cabang: number
  kode_cabang: string
  nama_cabang: string
  deskripsi?: string
  status: string
  created_at: string
  golongan_count?: number
}

interface Golongan {
  id_golongan: number
  kode_golongan: string
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  status: string
}

interface PaginatedCabangLomba {
  data: CabangLomba[]
  links: any[]
  from: number
  to: number
  total: number
}

const props = defineProps<{
  cabangLomba: PaginatedCabangLomba
  filters: {
    search?: string
    status?: string
  }
}>()

const filters = reactive({ ...props.filters })

const golonganDialog = ref({
  show: false,
  cabang: null as CabangLomba | null,
  golongan: [] as Golongan[],
  loading: false
})

const search = debounce(() => {
  router.get(route('admin.cabang-lomba.index'), filters, {
    preserveState: true,
    replace: true
  })
}, 300)

const clearFilters = () => {
  filters.search = ''
  filters.status = ''
  search()
}

const viewGolongan = async (cabang: CabangLomba) => {
  golonganDialog.value.cabang = cabang
  golonganDialog.value.show = true
  golonganDialog.value.loading = true
  golonganDialog.value.golongan = []

  try {
    const response = await fetch(route('admin.cabang-lomba.golongan', cabang.id_cabang))
    const golongan = await response.json()
    golonganDialog.value.golongan = golongan
  } catch (error) {
    console.error('Error fetching golongan:', error)
  } finally {
    golonganDialog.value.loading = false
  }
}

const deleteCabangLomba = (cabang: CabangLomba) => {
  if (confirm(`Apakah Anda yakin ingin menghapus cabang lomba ${cabang.nama_cabang}?`)) {
    router.delete(route('admin.cabang-lomba.destroy', cabang.id_cabang))
  }
}

const getStatusVariant = (status: string) => {
  const variants: Record<string, string> = {
    aktif: 'default',
    non_aktif: 'secondary'
  }
  return variants[status] || 'secondary'
}

const getStatusLabel = (status: string) => {
  const labels: Record<string, string> = {
    aktif: 'Aktif',
    non_aktif: 'Non Aktif'
  }
  return labels[status] || status
}

const formatDate = (date: string) => {
  return new Date(date).toLocaleDateString('id-ID', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  })
}

const formatCurrency = (amount: number) => {
  return new Intl.NumberFormat('id-ID').format(amount)
}

// Export/Import functionality
const showExportModal = ref(false)
const showImportModal = ref(false)

const handleImportSuccess = () => {
  // Refresh the page data
  router.reload()
}
</script>
