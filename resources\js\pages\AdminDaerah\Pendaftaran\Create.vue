<template>
  <AppLayout>
    <Head title="Daftarkan Peserta ke Lomba" />

    <div class="max-w-6xl mx-auto space-y-6">
      <!-- Header -->
      <div class="flex items-center justify-between">
        <div>
          <Heading title="Daftarkan Peserta ke Lomba" />
          <p class="text-gray-600 mt-1">
            Daftarkan peserta dari wilayah Anda ke golongan lomba yang tersedia
          </p>
        </div>
        <Button
          variant="outline"
          @click="$inertia.visit(route('admin-daerah.pendaftaran.index'))"
        >
          <Icon name="arrow-left" class="w-4 h-4 mr-2" />
          Kembali ke Daftar
        </Button>
      </div>

      <!-- Progress Steps -->
      <Card>
        <CardContent class="p-6">
          <div class="flex items-center justify-between">
            <div class="flex items-center space-x-2 md:space-x-4 overflow-x-auto">
              <div class="flex items-center flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full bg-blue-600 text-white text-sm font-medium">
                  1
                </div>
                <span class="ml-2 text-sm font-medium text-blue-600">Pilih Peserta</span>
              </div>
              <Icon name="chevron-right" class="w-4 h-4 text-gray-400 flex-shrink-0" />
              <div class="flex items-center flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full"
                     :class="form.id_peserta ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'">
                  2
                </div>
                <span class="ml-2 text-sm font-medium"
                      :class="form.id_peserta ? 'text-blue-600' : 'text-gray-500'">
                  Pilih Golongan
                </span>
              </div>
              <Icon name="chevron-right" class="w-4 h-4 text-gray-400 flex-shrink-0" />
              <div class="flex items-center flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full"
                     :class="form.id_golongan ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'">
                  3
                </div>
                <span class="ml-2 text-sm font-medium"
                      :class="form.id_golongan ? 'text-blue-600' : 'text-gray-500'">
                  Dokumen
                </span>
              </div>
              <Icon name="chevron-right" class="w-4 h-4 text-gray-400 flex-shrink-0" />
              <div class="flex items-center flex-shrink-0">
                <div class="flex items-center justify-center w-8 h-8 rounded-full"
                     :class="canSubmit ? 'bg-blue-600 text-white' : 'bg-gray-200 text-gray-500'">
                  4
                </div>
                <span class="ml-2 text-sm font-medium"
                      :class="canSubmit ? 'text-blue-600' : 'text-gray-500'">
                  Konfirmasi
                </span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      <form @submit.prevent="submit" class="space-y-6">
        <!-- Step 1: Peserta Selection -->
        <Card>
          <CardHeader>
            <CardTitle class="flex items-center">
              <div class="flex items-center justify-center w-6 h-6 rounded-full bg-blue-600 text-white text-sm font-medium mr-3">
                1
              </div>
              Pilih Peserta
            </CardTitle>
            <CardDescription>
              Pilih peserta yang akan didaftarkan ke lomba
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label for="id_peserta">Peserta *</Label>
              <Select v-model="form.id_peserta" required>
                <SelectTrigger class="h-12">
                  <SelectValue placeholder="Cari dan pilih peserta..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="p in peserta" :key="p.id_peserta" :value="p.id_peserta.toString()">
                    <div class="flex items-center space-x-3 py-2">
                      <Avatar class="h-8 w-8">
                        <AvatarFallback class="text-xs">
                          {{ p.nama_lengkap.charAt(0) }}
                        </AvatarFallback>
                      </Avatar>
                      <div class="flex flex-col">
                        <span class="font-medium">{{ p.nama_lengkap }}</span>
                        <span class="text-sm text-gray-500">{{ p.nik }} • {{ p.user?.email }}</span>
                      </div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <InputError :message="errors.id_peserta" />
            </div>

            <!-- Selected Peserta Info -->
            <div v-if="selectedPesertaInfo" class="mt-4 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 class="font-medium text-blue-900 mb-3 flex items-center">
                <Icon name="user" class="w-4 h-4 mr-2" />
                Informasi Peserta Terpilih
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div class="flex items-center space-x-3">
                  <Avatar class="h-12 w-12">
                    <AvatarFallback>
                      {{ selectedPesertaInfo.nama_lengkap.charAt(0) }}
                    </AvatarFallback>
                  </Avatar>
                  <div>
                    <p class="font-medium text-blue-900">{{ selectedPesertaInfo.nama_lengkap }}</p>
                    <p class="text-sm text-blue-700">{{ selectedPesertaInfo.user?.email }}</p>
                  </div>
                </div>
                <div class="space-y-2 text-sm">
                  <div class="flex justify-between">
                    <span class="text-blue-700">NIK:</span>
                    <span class="font-medium">{{ selectedPesertaInfo.nik }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Jenis Kelamin:</span>
                    <span class="font-medium">{{ selectedPesertaInfo.jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan' }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Tanggal Lahir:</span>
                    <span class="font-medium">{{ formatDate(selectedPesertaInfo.tanggal_lahir) }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-blue-700">Umur:</span>
                    <span class="font-medium">{{ calculateAge(selectedPesertaInfo.tanggal_lahir) }} tahun</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Step 2: Golongan Selection -->
        <Card :class="{ 'opacity-50': !form.id_peserta }">
          <CardHeader>
            <CardTitle class="flex items-center">
              <div class="flex items-center justify-center w-6 h-6 rounded-full mr-3"
                   :class="form.id_peserta ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-500'">
                2
              </div>
              Pilih Golongan Lomba
            </CardTitle>
            <CardDescription>
              Pilih golongan lomba yang sesuai dengan kriteria peserta
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="space-y-2">
              <Label for="id_golongan">Golongan Lomba *</Label>
              <Select v-model="form.id_golongan" :disabled="!form.id_peserta" required>
                <SelectTrigger class="h-12">
                  <SelectValue placeholder="Pilih golongan lomba yang tersedia..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem v-for="g in availableGolongan" :key="g.id_golongan" :value="g.id_golongan.toString()">
                    <div class="flex flex-col py-2">
                      <div class="flex items-center justify-between">
                        <span class="font-medium">{{ g.nama_golongan }}</span>
                        <Badge variant="outline" class="ml-2">
                          {{ g.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}
                        </Badge>
                      </div>
                      <span class="text-sm text-gray-500 mt-1">
                        {{ g.cabang_lomba?.nama_cabang }}
                      </span>
                      <div class="flex items-center justify-between mt-1">
                        <span class="text-xs text-gray-400">
                          Umur {{ g.batas_umur_min }}-{{ g.batas_umur_max }} tahun
                        </span>
                        <!--<span class="text-sm font-medium text-green-600">
                          {{ formatCurrency(g.biaya_pendaftaran) }}
                        </span>-->
                      </div>
                    </div>
                  </SelectItem>
                </SelectContent>
              </Select>
              <InputError :message="errors.id_golongan" />

              <div v-if="form.id_peserta && availableGolongan.length === 0" class="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div class="flex items-center">
                  <Icon name="alert-triangle" class="w-4 h-4 text-yellow-600 mr-2" />
                  <span class="text-sm text-yellow-800">
                    Tidak ada golongan yang sesuai dengan kriteria peserta ini.
                  </span>
                </div>
              </div>
            </div>

            <!-- Selected Golongan Info -->
            <div v-if="selectedGolonganInfo" class="mt-4 p-4 bg-green-50 rounded-lg border border-green-200">
              <h4 class="font-medium text-green-900 mb-3 flex items-center">
                <Icon name="trophy" class="w-4 h-4 mr-2" />
                Informasi Golongan Terpilih
              </h4>
              <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-green-700">Cabang Lomba:</span>
                    <span class="font-medium">{{ selectedGolonganInfo.cabang_lomba?.nama_cabang }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">Golongan:</span>
                    <span class="font-medium">{{ selectedGolonganInfo.nama_golongan }}</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">Kategori:</span>
                    <span class="font-medium">{{ selectedGolonganInfo.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}</span>
                  </div>
                </div>
                <div class="space-y-2">
                  <div class="flex justify-between">
                    <span class="text-green-700">Batas Umur:</span>
                    <span class="font-medium">{{ selectedGolonganInfo.batas_umur_min }}-{{ selectedGolonganInfo.batas_umur_max }} tahun</span>
                  </div>
                  <div class="flex justify-between">
                    <span class="text-green-700">Kuota Maksimal:</span>
                    <span class="font-medium">{{ selectedGolonganInfo.kuota_max }} peserta</span>
                  </div>
                  <!--<div class="flex justify-between">
                    <span class="text-green-700">Biaya Pendaftaran:</span>
                    <span class="font-medium text-lg text-green-800">{{ formatCurrency(selectedGolonganInfo.biaya_pendaftaran) }}</span>
                  </div> -->
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Step 3: Document Upload (Optional) -->
        <Card :class="{ 'opacity-50': !form.id_golongan }">
          <CardHeader>
            <CardTitle class="flex items-center">
              <div class="flex items-center justify-center w-6 h-6 rounded-full mr-3"
                   :class="form.id_golongan ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-500'">
                3
              </div>
              Upload Dokumen (Opsional)
            </CardTitle>
            <CardDescription>
              Upload dokumen pendukung untuk pendaftaran ini (dapat dilakukan setelah pendaftaran juga)
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div v-for="doc in filteredRequiredDocuments" :key="doc.id" class="space-y-2">
                <Label :for="`doc_${doc.document_type.code}`">{{ doc.document_type.name }} {{ doc.is_required ? '*' : '' }} </Label>
                <div class="flex items-center space-x-2">
                  <Input
                    :id="`doc_${doc.document_type.code}`"
                    type="file"
                    accept=".jpg,.jpeg,.png,.pdf"
                    @change="handleFileUpload(doc.document_type.code, $event)"
                    :disabled="!form.id_golongan"
                    class="flex-1"
                  />
                  <Button
                    v-if="uploadedDocuments[doc.document_type.code]"
                    type="button"
                    variant="outline"
                    size="sm"
                    @click="removeDocument(doc.document_type.code)"
                    class="text-red-600"
                  >
                    <Icon name="x" class="w-4 h-4" />
                  </Button>
                </div>
                <p v-if="doc.is_required" class="text-xs text-red-600 flex items-center">
                  <Icon name="alert-circle" class="w-3 h-3 mr-1" />
                  Wajib diupload
                </p>
                <InputError :message="errors[`documents.${doc.document_type.code}`]" />
                <p v-if="uploadedDocuments[doc.document_type.code]" class="text-xs text-green-600 flex items-center">
                  <Icon name="check" class="w-3 h-3 mr-1" />
                  {{ uploadedDocuments[doc.document_type.code].name }}
                </p>
              </div>
            </div>
            <div class="p-3 bg-gray-50 rounded-lg">
              <p class="text-sm text-gray-600 flex items-center">
                <Icon name="info" class="w-4 h-4 mr-2" />
                Format yang didukung: JPG, PNG, PDF. Maksimal 5MB per file. Dokumen dapat diupload setelah pendaftaran juga.
              </p>
            </div>
          </CardContent>
        </Card>

        <!-- Step 4: Additional Settings -->
        <Card :class="{ 'opacity-50': !canSubmit }">
          <CardHeader>
            <CardTitle class="flex items-center">
              <div class="flex items-center justify-center w-6 h-6 rounded-full mr-3"
                   :class="canSubmit ? 'bg-blue-600 text-white' : 'bg-gray-300 text-gray-500'">
                4
              </div>
              Pengaturan Tambahan
            </CardTitle>
            <CardDescription>
              Atur nomor urut dan konfirmasi pendaftaran
            </CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <!-- Nomor Urut (Optional) -->
            <div class="space-y-2">
              <Label for="nomor_urut">Nomor Urut (Opsional)</Label>
              <Input
                id="nomor_urut"
                v-model="form.nomor_urut"
                type="number"
                min="1"
                placeholder="Kosongkan untuk nomor otomatis"
                :disabled="!canSubmit"
                class="h-12"
              />
              <p class="text-sm text-gray-500 flex items-center">
                <Icon name="info" class="w-4 h-4 mr-1" />
                Jika dikosongkan, nomor urut akan dibuat otomatis sesuai urutan pendaftaran
              </p>
              <InputError :message="errors.nomor_urut" />
            </div>

            <!-- Age Validation Warning -->
            <div v-if="ageValidationMessage" class="p-4 rounded-lg border"
                 :class="ageValidationMessage.type === 'error' ? 'bg-red-50 border-red-200 text-red-700' : 'bg-yellow-50 border-yellow-200 text-yellow-700'">
              <div class="flex">
                <Icon :name="ageValidationMessage.type === 'error' ? 'alert-circle' : 'alert-triangle'" class="w-5 h-5 mr-2 mt-0.5 flex-shrink-0" />
                <div>
                  <p class="font-medium">{{ ageValidationMessage.title }}</p>
                  <p class="text-sm mt-1">{{ ageValidationMessage.message }}</p>
                </div>
              </div>
            </div>

            <!-- Registration Summary -->
            <div v-if="canSubmit" class="p-4 bg-blue-50 rounded-lg border border-blue-200">
              <h4 class="font-medium text-blue-900 mb-3 flex items-center">
                <Icon name="check-circle" class="w-4 h-4 mr-2" />
                Ringkasan Pendaftaran
              </h4>
              <div class="space-y-2 text-sm">
                <div class="flex justify-between">
                  <span class="text-blue-700">Peserta:</span>
                  <span class="font-medium">{{ selectedPesertaInfo?.nama_lengkap }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-blue-700">Golongan:</span>
                  <span class="font-medium">{{ selectedGolonganInfo?.nama_golongan }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-blue-700">Cabang Lomba:</span>
                  <span class="font-medium">{{ selectedGolonganInfo?.cabang_lomba?.nama_cabang }}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-blue-700">Biaya Pendaftaran:</span>
                  <span class="font-medium text-lg">{{ formatCurrency(selectedGolonganInfo?.biaya_pendaftaran || 0) }}</span>
                </div>
              </div>
            </div>

            <!-- Form Actions -->
            <div class="flex justify-between pt-6">
              <Button
                type="button"
                variant="outline"
                @click="$inertia.visit(route('admin-daerah.pendaftaran.index'))"
              >
                <Icon name="arrow-left" class="w-4 h-4 mr-2" />
                Kembali
              </Button>
              <Button
                type="submit"
                :disabled="processing || !canSubmit"
                class="min-w-[160px]"
              >
                <Icon v-if="processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
                <Icon v-else name="save" class="w-4 h-4 mr-2" />
                {{ processing ? 'Mendaftarkan...' : 'Daftarkan Peserta' }}
              </Button>
            </div>
          </CardContent>
        </Card>
      </form>
    </div>
  </AppLayout>
</template>

<script setup lang="ts">
import { computed, watch, ref } from 'vue'
import { Head, useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'

interface CabangLomba {
  nama_cabang: string
  deskripsi: string
}

interface Golongan {
  id_golongan: number
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  cabang_lomba: CabangLomba
}

interface User {
  email: string
}

interface Peserta {
  id_peserta: number
  nama_lengkap: string
  nik: string
  jenis_kelamin: string
  tanggal_lahir: string
  user?: User
}

// Dokumen
interface DocumentType {
  id_document_type: number
  code: string
  name: string
  description: string
  is_required: boolean
  allowed_file_types: string[]
  max_file_size: number
}

interface GolonganDocumentRequirement {
  id: number
  golongan_id: number
  document_type_id: number
  is_required: boolean
  documentType: DocumentType
}


interface Props {
  peserta: Peserta[]
  golongan: Golongan[]
  selectedPeserta?: Peserta
  errors: Record<string, string>
  requiredDocuments: GolonganDocumentRequirement[]
}

const props = defineProps<Props>()

const form = useForm({
  id_peserta: props.selectedPeserta?.id_peserta?.toString() || '',
  id_golongan: '',
  nomor_urut: '',
  documents: {} as Record<string, File>
})

// Document management
const uploadedDocuments = ref<Record<string, File>>({})

// const requiredDocuments = {
//   'foto': 'Foto Peserta',
//   'ktp': 'KTP/Identitas',
//   'kartu_keluarga': 'Kartu Keluarga',
//   'surat_rekomendasi': 'Surat Rekomendasi',
//   'ijazah': 'Ijazah Terakhir',
//   'sertifikat': 'Sertifikat Pendukung'
// }

// filter required documents based on golongan
const filteredRequiredDocuments = computed(() => {
    return props.requiredDocuments.filter(doc => doc.id_golongan === parseInt(form.id_golongan))

})

const processing = computed(() => form.processing)

const selectedPesertaInfo = computed(() => {
  if (!form.id_peserta) return null
  return props.peserta.find(p => p.id_peserta.toString() === form.id_peserta)
})

const selectedGolonganInfo = computed(() => {
  if (!form.id_golongan) return null
  return props.golongan.find(g => g.id_golongan.toString() === form.id_golongan)
})

const availableGolongan = computed(() => {
  if (!selectedPesertaInfo.value) return []

  // Filter golongan based on gender and age
  return props.golongan.filter(g => {
    // Check gender
    if (g.jenis_kelamin !== selectedPesertaInfo.value!.jenis_kelamin) return false

    // Check age
    const age = calculateAge(selectedPesertaInfo.value!.tanggal_lahir)
    if (age < g.batas_umur_min || age > g.batas_umur_max) return false

    return true
  })
})

const ageValidationMessage = computed(() => {
  if (!selectedPesertaInfo.value || !selectedGolonganInfo.value) return null

  const age = calculateAge(selectedPesertaInfo.value.tanggal_lahir)
  const minAge = selectedGolonganInfo.value.batas_umur_min
  const maxAge = selectedGolonganInfo.value.batas_umur_max

  if (age < minAge || age > maxAge) {
    return {
      type: 'error',
      title: 'Umur Tidak Sesuai',
      message: `Peserta berumur ${age} tahun, sedangkan golongan ini untuk umur ${minAge}-${maxAge} tahun.`
    }
  }

  return null
})

const canSubmit = computed(() => {
  return form.id_peserta && form.id_golongan && ageValidationMessage.value?.type !== 'error'
})

// Reset golongan when peserta changes
watch(() => form.id_peserta, () => {
  form.id_golongan = ''
})

function calculateAge(birthDate: string): number {
  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

function formatDate(date: string): string {
  return new Date(date).toLocaleDateString('id-ID', {
    day: 'numeric',
    month: 'long',
    year: 'numeric'
  })
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount)
}

function handleFileUpload(documentType: string, event: Event) {
  const target = event.target as HTMLInputElement
  const file = target.files?.[0]

  if (file) {
    // Validate file size (5MB max)
    if (file.size > 5 * 1024 * 1024) {
      alert('Ukuran file tidak boleh lebih dari 5MB')
      target.value = ''
      return
    }

    // Validate file type
    const allowedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'application/pdf']
    if (!allowedTypes.includes(file.type)) {
      alert('Format file harus JPG, PNG, atau PDF')
      target.value = ''
      return
    }

    uploadedDocuments.value[documentType] = file
    form.documents[documentType] = file
  }
}

function removeDocument(documentType: string) {
  delete uploadedDocuments.value[documentType]
  delete form.documents[documentType]

  // Clear the file input
  const input = document.getElementById(`doc_${documentType}`) as HTMLInputElement
  if (input) {
    input.value = ''
  }
}

function submit() {
  // Create FormData to handle file uploads
  const formData = new FormData()
  formData.append('id_peserta', form.id_peserta)
  formData.append('id_golongan', form.id_golongan)
  if (form.nomor_urut) {
    formData.append('nomor_urut', form.nomor_urut)
  }

  // Add documents
  Object.entries(form.documents).forEach(([type, file]) => {
    formData.append(`documents[${type}]`, file)
  })

  // Use router.post with FormData
  form.post(route('admin-daerah.pendaftaran.store'), {
    forceFormData: true
  })
}
</script>
