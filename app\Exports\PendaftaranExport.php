<?php

namespace App\Exports;

use App\Models\Pendaftaran;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class PendaftaranExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;
    protected $userRole;
    protected $userWilayah;

    public function __construct(array $filters = [], $userRole = null, $userWilayah = null)
    {
        $this->filters = $filters;
        $this->userRole = $userRole;
        $this->userWilayah = $userWilayah;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'mimbar',
            'pembayaran',
            'dokumenPeserta'
        ]);

        // Apply role-based filtering
        if ($this->userRole === 'admin_daerah' && $this->userWilayah) {
            $query->whereHas('peserta', function($q) {
                $q->where('id_wilayah', $this->userWilayah);
            });
        }

        // Apply filters
        if (!empty($this->filters['search'])) {
            $search = $this->filters['search'];
            $query->where(function ($q) use ($search) {
                $q->whereHas('peserta', function ($q) use ($search) {
                    $q->where('nama_lengkap', 'like', "%{$search}%")
                      ->orWhere('nik', 'like', "%{$search}%");
                })
                ->orWhere('nomor_pendaftaran', 'like', "%{$search}%")
                ->orWhere('nomor_peserta', 'like', "%{$search}%");
            });
        }

        if (!empty($this->filters['status'])) {
            $query->where('status_pendaftaran', $this->filters['status']);
        }

        if (!empty($this->filters['golongan'])) {
            $query->where('id_golongan', $this->filters['golongan']);
        }

        if (!empty($this->filters['tahun'])) {
            $query->where('tahun_pendaftaran', $this->filters['tahun']);
        }

        if (!empty($this->filters['regional_verification_status'])) {
            $query->where('regional_verification_status', $this->filters['regional_verification_status']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Nomor Pendaftaran',
            'Nomor Peserta',
            'Nomor Urut',
            'Tahun Pendaftaran',
            'NIK Peserta',
            'Nama Peserta',
            'Email Peserta',
            'Jenis Kelamin',
            'Tanggal Lahir',
            'Wilayah',
            'Cabang Lomba',
            'Golongan',
            'Mimbar',
            'Status Pendaftaran',
            'Status Verifikasi Regional',
            'Tanggal Verifikasi Regional',
            'Catatan Verifikasi Regional',
            'Tanggal Verifikasi',
            'Diverifikasi Oleh',
            'Catatan Verifikasi',
            'Tanggal Approval',
            'Diapprove Oleh',
            'Catatan Approval',
            'Status Pembayaran',
            'Jumlah Pembayaran',
            'Tanggal Pembayaran',
            'Dokumen Lengkap',
            'Keterangan',
            'Tanggal Daftar',
            'Tanggal Dibuat',
            'Tanggal Diperbarui'
        ];
    }

    /**
     * @param mixed $pendaftaran
     * @return array
     */
    public function map($pendaftaran): array
    {
        return [
            $pendaftaran->nomor_pendaftaran,
            $pendaftaran->nomor_peserta,
            $pendaftaran->nomor_urut,
            $pendaftaran->tahun_pendaftaran,
            $pendaftaran->peserta->nik ?? '',
            $pendaftaran->peserta->nama_lengkap ?? '',
            $pendaftaran->peserta->email ?? $pendaftaran->peserta->user->email ?? '',
            $pendaftaran->peserta->jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan',
            $pendaftaran->peserta->tanggal_lahir ? $pendaftaran->peserta->tanggal_lahir->format('d/m/Y') : '',
            $pendaftaran->peserta->wilayah->nama_wilayah ?? '',
            $pendaftaran->golongan->cabangLomba->nama_cabang ?? '',
            $pendaftaran->golongan->nama_golongan ?? '',
            $pendaftaran->mimbar->nama_mimbar ?? '',
            $this->getStatusText($pendaftaran->status_pendaftaran),
            $this->getVerificationStatusText($pendaftaran->regional_verification_status),
            $pendaftaran->regional_verified_at ? $pendaftaran->regional_verified_at->format('d/m/Y H:i') : '',
            $pendaftaran->regional_verification_notes ?? '',
            $pendaftaran->verified_at ? $pendaftaran->verified_at->format('d/m/Y H:i') : '',
            $pendaftaran->verifiedBy->nama_lengkap ?? '',
            $pendaftaran->catatan_verifikasi ?? '',
            $pendaftaran->approved_at ? $pendaftaran->approved_at->format('d/m/Y H:i') : '',
            $pendaftaran->approvedBy->nama_lengkap ?? '',
            $pendaftaran->catatan_approval ?? '',
            $pendaftaran->pembayaran ? $this->getPaymentStatusText($pendaftaran->pembayaran->status_pembayaran) : 'Belum Bayar',
            $pendaftaran->pembayaran ? number_format($pendaftaran->pembayaran->jumlah_bayar, 0, ',', '.') : '0',
            $pendaftaran->pembayaran && $pendaftaran->pembayaran->tanggal_bayar ? $pendaftaran->pembayaran->tanggal_bayar->format('d/m/Y H:i') : '',
            $this->getDocumentStatus($pendaftaran),
            $pendaftaran->keterangan ?? '',
            $pendaftaran->tanggal_daftar ? $pendaftaran->tanggal_daftar->format('d/m/Y H:i') : '',
            $pendaftaran->created_at->format('d/m/Y H:i'),
            $pendaftaran->updated_at->format('d/m/Y H:i')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }

    private function getStatusText($status)
    {
        $statuses = [
            'draft' => 'Draft',
            'submitted' => 'Disubmit',
            'verified' => 'Terverifikasi',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak'
        ];

        return $statuses[$status] ?? $status;
    }

    private function getVerificationStatusText($status)
    {
        $statuses = [
            'pending' => 'Menunggu',
            'verified' => 'Terverifikasi',
            'rejected' => 'Ditolak'
        ];

        return $statuses[$status] ?? $status;
    }

    private function getPaymentStatusText($status)
    {
        $statuses = [
            'pending' => 'Menunggu',
            'paid' => 'Lunas',
            'failed' => 'Gagal',
            'cancelled' => 'Dibatalkan'
        ];

        return $statuses[$status] ?? $status;
    }

    private function getDocumentStatus($pendaftaran)
    {
        if (!$pendaftaran->dokumenPeserta || $pendaftaran->dokumenPeserta->isEmpty()) {
            return 'Belum Upload';
        }

        $totalRequired = $pendaftaran->dokumenPeserta->count();
        $verified = $pendaftaran->dokumenPeserta->where('status_verifikasi', 'verified')->count();

        if ($verified === $totalRequired) {
            return 'Lengkap & Terverifikasi';
        } elseif ($verified > 0) {
            return 'Sebagian Terverifikasi';
        } else {
            return 'Belum Terverifikasi';
        }
    }
}
