<?php

namespace App\Imports;

use App\Models\Pendaftaran;
use App\Models\Peserta;
use App\Models\Golongan;
use App\Models\Mimbar;
use App\Services\RegistrationNumberService;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Validators\Failure;

class PendaftaranImport implements ToCollection, WithHeadingRow, SkipsOnFailure
{
    use SkipsFailures;

    protected $importResults = [
        'success' => 0,
        'failed' => 0,
        'errors' => [],
        'created' => [],
        'updated' => []
    ];

    protected $pesertaCache = [];
    protected $golonganCache = [];
    protected $mimbarCache = [];
    protected $userRole;
    protected $userWilayah;

    public function __construct($userRole = null, $userWilayah = null)
    {
        $this->userRole = $userRole;
        $this->userWilayah = $userWilayah;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        // Cache data for performance
        $this->pesertaCache = Peserta::with('wilayah')->get()->keyBy('nik');
        $this->golonganCache = Golongan::pluck('id_golongan', 'nama_golongan')->toArray();
        $this->mimbarCache = Mimbar::pluck('id_mimbar', 'nama_mimbar')->toArray();

        foreach ($collection as $row) {
            try {
                $this->processRow($row);
            } catch (\Exception $e) {
                $this->importResults['failed']++;
                $this->importResults['errors'][] = [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    protected function processRow($row)
    {
        // Skip empty rows
        $rowArray = is_array($row) ? $row : $row->toArray();
        if (empty(array_filter($rowArray))) {
            return;
        }

        // Clean and validate data
        $data = [
            'nik_peserta' => trim($row['nik_peserta'] ?? ''),
            'golongan' => trim($row['golongan'] ?? ''),
            'mimbar' => trim($row['mimbar'] ?? ''),
            'tahun_pendaftaran' => (int) ($row['tahun_pendaftaran'] ?? date('Y')),
            'status_pendaftaran' => $this->normalizeStatus($row['status_pendaftaran'] ?? 'draft'),
            'regional_verification_status' => $this->normalizeVerificationStatus($row['regional_verification_status'] ?? 'pending'),
            'keterangan' => trim($row['keterangan'] ?? '')
        ];

        // Validate required fields
        if (empty($data['nik_peserta']) || empty($data['golongan'])) {
            throw new \Exception('NIK peserta dan golongan wajib diisi');
        }

        // Find peserta
        if (!isset($this->pesertaCache[$data['nik_peserta']])) {
            throw new \Exception("Peserta dengan NIK '{$data['nik_peserta']}' tidak ditemukan");
        }
        $peserta = $this->pesertaCache[$data['nik_peserta']];

        // Role-based validation for admin daerah
        if ($this->userRole === 'admin_daerah' && $this->userWilayah) {
            if ($peserta->id_wilayah != $this->userWilayah) {
                throw new \Exception("Admin daerah hanya dapat mendaftarkan peserta dari wilayahnya sendiri");
            }
        }

        // Find golongan
        if (!isset($this->golonganCache[$data['golongan']])) {
            throw new \Exception("Golongan '{$data['golongan']}' tidak ditemukan");
        }
        $golonganId = $this->golonganCache[$data['golongan']];

        // Find mimbar (optional)
        $mimbarId = null;
        if (!empty($data['mimbar'])) {
            if (!isset($this->mimbarCache[$data['mimbar']])) {
                throw new \Exception("Mimbar '{$data['mimbar']}' tidak ditemukan");
            }
            $mimbarId = $this->mimbarCache[$data['mimbar']];
        }

        // Validate year
        if ($data['tahun_pendaftaran'] < 2020 || $data['tahun_pendaftaran'] > (date('Y') + 1)) {
            throw new \Exception("Tahun pendaftaran tidak valid: {$data['tahun_pendaftaran']}");
        }

        // Check if registration already exists
        $existingPendaftaran = Pendaftaran::where('id_peserta', $peserta->id_peserta)
                                         ->where('id_golongan', $golonganId)
                                         ->where('tahun_pendaftaran', $data['tahun_pendaftaran'])
                                         ->first();

        DB::transaction(function () use ($data, $peserta, $golonganId, $mimbarId, $existingPendaftaran) {
            if ($existingPendaftaran) {
                // Update existing registration
                $this->updateExistingPendaftaran($existingPendaftaran, $data, $mimbarId);
                $this->importResults['updated'][] = $existingPendaftaran->nomor_pendaftaran;
            } else {
                // Create new registration
                $pendaftaran = $this->createNewPendaftaran($data, $peserta, $golonganId, $mimbarId);
                $this->importResults['created'][] = $pendaftaran->nomor_pendaftaran;
            }
        });

        $this->importResults['success']++;
    }

    protected function createNewPendaftaran($data, $peserta, $golonganId, $mimbarId)
    {
        // Get golongan for number generation
        $golongan = Golongan::find($golonganId);
        
        // Generate registration numbers
        $numbers = RegistrationNumberService::generateAllNumbers($golongan, $data['tahun_pendaftaran']);

        return Pendaftaran::create([
            'id_peserta' => $peserta->id_peserta,
            'id_golongan' => $golonganId,
            'id_mimbar' => $mimbarId,
            'nomor_pendaftaran' => $numbers['nomor_pendaftaran'],
            'nomor_peserta' => $numbers['nomor_peserta'],
            'nomor_urut' => $numbers['nomor_urut'],
            'tahun_pendaftaran' => $data['tahun_pendaftaran'],
            'status_pendaftaran' => $data['status_pendaftaran'],
            'regional_verification_status' => $data['regional_verification_status'],
            'keterangan' => $data['keterangan'] ?: null,
            'tanggal_daftar' => now()
        ]);
    }

    protected function updateExistingPendaftaran($pendaftaran, $data, $mimbarId)
    {
        $pendaftaran->update([
            'id_mimbar' => $mimbarId,
            'status_pendaftaran' => $data['status_pendaftaran'],
            'regional_verification_status' => $data['regional_verification_status'],
            'keterangan' => $data['keterangan'] ?: null
        ]);
    }

    protected function normalizeStatus($status)
    {
        $status = strtolower(trim($status));
        
        $validStatuses = ['draft', 'submitted', 'verified', 'approved', 'rejected'];
        
        if (in_array($status, $validStatuses)) {
            return $status;
        }
        
        return 'draft'; // Default
    }

    protected function normalizeVerificationStatus($status)
    {
        $status = strtolower(trim($status));
        
        $validStatuses = ['pending', 'verified', 'rejected'];
        
        if (in_array($status, $validStatuses)) {
            return $status;
        }
        
        return 'pending'; // Default
    }

    /**
     * Get import results
     */
    public function getImportResults()
    {
        return $this->importResults;
    }

    /**
     * @param Failure[] $failures
     */
    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->importResults['failed']++;
            $this->importResults['errors'][] = [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(),
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ];
        }
    }
}
