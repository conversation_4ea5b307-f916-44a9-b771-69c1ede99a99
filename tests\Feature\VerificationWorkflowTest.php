<?php

namespace Tests\Feature;

use Tests\TestCase;
use App\Models\User;
use App\Models\Peserta;
use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\CabangLomba;
use App\Models\DocumentType;
use App\Models\GolonganDocumentRequirement;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;

class VerificationWorkflowTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $wilayah;
    protected $cabangLomba;
    protected $golongan;
    protected $documentType;
    protected $adminDaerah;
    protected $adminProvinsi;
    protected $pesertaUser;

    protected function setUp(): void
    {
        parent::setUp();

        // Create test data
        $this->createTestData();
    }

    private function createTestData(): void
    {
        // Use existing data or create simple test data
        $this->wilayah = Wilayah::first() ?: Wilayah::create([
            'nama_wilayah' => 'Test Wilayah',
            'kode_wilayah' => 'TW01',
            'level_wilayah' => 'kabupaten'
        ]);

        $this->cabangLomba = CabangLomba::first() ?: CabangLomba::create([
            'nama_cabang' => 'Test Cabang',
            'deskripsi' => 'Test Description'
        ]);

        $this->golongan = Golongan::first() ?: Golongan::create([
            'id_cabang_lomba' => $this->cabangLomba->id_cabang_lomba,
            'nama_golongan' => 'Test Golongan',
            'deskripsi' => 'Test Description',
            'biaya_pendaftaran' => 100000
        ]);

        $this->documentType = DocumentType::first() ?: DocumentType::create([
            'name' => 'Test Document',
            'description' => 'Test Document Description'
        ]);

        GolonganDocumentRequirement::firstOrCreate([
            'id_golongan' => $this->golongan->id_golongan,
            'document_type_id' => $this->documentType->id_document_type,
        ], [
            'is_required' => true
        ]);

        // Create test users
        $this->adminDaerah = User::create([
            'username' => 'admin_daerah_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin_daerah',
            'id_wilayah' => $this->wilayah->id_wilayah
        ]);

        $this->adminProvinsi = User::create([
            'username' => 'admin_provinsi_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'admin'
        ]);

        $this->pesertaUser = User::create([
            'username' => 'peserta_test',
            'email' => '<EMAIL>',
            'password' => bcrypt('password'),
            'role' => 'peserta'
        ]);
    }

    /** @test */
    public function self_registered_participant_requires_regional_verification()
    {
        // Create self-registered participant
        $peserta = Peserta::create([
            'id_user' => $this->pesertaUser->id_user,
            'nik' => '1234567890123456',
            'nama_lengkap' => 'Test Peserta',
            'tempat_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending',
            'documents_complete' => false
        ]);

        // Should not be visible to provincial admin
        $this->assertFalse($peserta->canBeSeenByProvincialAdmin());

        // Should be visible to regional admin
        $this->assertTrue($peserta->needsRegionalVerification());
    }

    /** @test */
    public function admin_registered_participant_bypasses_regional_verification()
    {
        // Create admin-registered participant
        $peserta = Peserta::create([
            'id_user' => $this->pesertaUser->id_user,
            'nik' => '1234567890123457',
            'nama_lengkap' => 'Test Admin Peserta',
            'tempat_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'admin_daerah',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        // Should be visible to provincial admin (bypasses regional verification)
        $this->assertTrue($peserta->canBeSeenByProvincialAdmin());

        // Should not need regional verification
        $this->assertFalse($peserta->needsRegionalVerification());
    }

    /** @test */
    public function verification_scopes_work_correctly()
    {
        // Create participants with different statuses
        $selfRegisteredPending = Peserta::create([
            'id_user' => $this->pesertaUser->id_user,
            'nik' => '1234567890123458',
            'nama_lengkap' => 'Self Pending',
            'tempat_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending',
            'documents_complete' => false
        ]);

        $selfRegisteredVerifiedComplete = Peserta::create([
            'id_user' => $this->pesertaUser->id_user,
            'nik' => '1234567890123459',
            'nama_lengkap' => 'Self Verified Complete',
            'tempat_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'verified',
            'documents_complete' => true
        ]);

        // Test visibility scopes
        $visibleToProvincial = Peserta::visibleToProvincialAdmin()->get();

        $this->assertFalse($visibleToProvincial->contains($selfRegisteredPending));
        $this->assertTrue($visibleToProvincial->contains($selfRegisteredVerifiedComplete));
    }

    /** @test */
    public function helper_methods_work_correctly()
    {
        // Create self-registered participant
        $selfRegistered = Peserta::create([
            'id_user' => $this->pesertaUser->id_user,
            'nik' => '1234567890123460',
            'nama_lengkap' => 'Self Registered',
            'tempat_lahir' => 'Test City',
            'tanggal_lahir' => '1990-01-01',
            'jenis_kelamin' => 'L',
            'alamat' => 'Test Address',
            'id_wilayah' => $this->wilayah->id_wilayah,
            'registration_type' => 'mandiri',
            'regional_verification_status' => 'pending',
            'documents_complete' => false
        ]);

        // Test helper methods
        $this->assertTrue($selfRegistered->needsRegionalVerification());
        $this->assertFalse($selfRegistered->isRegionalVerified());
        $this->assertFalse($selfRegistered->canBeSeenByProvincialAdmin());
        $this->assertTrue($selfRegistered->requiresRegionalVerification());
        $this->assertFalse($selfRegistered->isAdminRegistered());
    }

    /** @test */
    public function middleware_applies_correct_visibility_rules()
    {
        // Create test middleware
        $middleware = new \App\Http\Middleware\ParticipantVisibilityMiddleware();

        // Create mock request
        $request = new \Illuminate\Http\Request();

        // Test provincial admin rules
        $user = $this->adminProvinsi;
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        $middleware->handle($request, function ($req) {
            $this->assertEquals('provincial_admin', $req->_visibility_constraint);
            $this->assertTrue($req->_visibility_rules['admin_registered_complete']);
            $this->assertTrue($req->_visibility_rules['self_registered_verified_complete']);
            return response('OK');
        });

        // Test regional admin rules
        $user = $this->adminDaerah;
        $request = new \Illuminate\Http\Request();
        $request->setUserResolver(function () use ($user) {
            return $user;
        });

        $middleware->handle($request, function ($req) use ($user) {
            $this->assertEquals('regional_admin', $req->_visibility_constraint);
            $this->assertEquals($user->id_wilayah, $req->_visibility_rules['region_filter']);
            $this->assertTrue($req->_visibility_rules['all_statuses']);
            return response('OK');
        });
    }

    /** @test */
    public function payment_functionality_is_disabled()
    {
        // Create payment middleware
        $middleware = new \App\Http\Middleware\PaymentDisabledMiddleware();

        // Create mock request with payment-related URI
        $request = \Illuminate\Http\Request::create('/pembayaran/123', 'GET');

        // Test JSON response
        $request->headers->set('Accept', 'application/json');
        $response = $middleware->handle($request, function () {
            return response('This should not be reached');
        });

        $this->assertEquals(503, $response->getStatusCode());
        $this->assertStringContainsString('Payment functionality is temporarily disabled', $response->getContent());
    }
}
