<?php

namespace App\Imports;

use App\Models\Peserta;
use App\Models\User;
use App\Models\Wilayah;
use App\Rules\UniqueNikAcrossTables;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Validators\Failure;

class PesertaImport implements ToCollection, WithHeadingRow, SkipsOnFailure
{
    use SkipsFailures;

    protected $importResults = [
        'success' => 0,
        'failed' => 0,
        'errors' => [],
        'created' => [],
        'updated' => []
    ];

    protected $wilayahCache = [];
    protected $userRole;
    protected $userWilayah;
    protected $registeredBy;

    public function __construct($userRole = null, $userWilayah = null, $registeredBy = null)
    {
        $this->userRole = $userRole;
        $this->userWilayah = $userWilayah;
        $this->registeredBy = $registeredBy;
    }

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        // Cache wilayah for performance
        $this->wilayahCache = Wilayah::pluck('id_wilayah', 'nama_wilayah')->toArray();

        foreach ($collection as $row) {
            try {
                $this->processRow($row);
            } catch (\Exception $e) {
                $this->importResults['failed']++;
                $this->importResults['errors'][] = [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    protected function processRow($row)
    {
        // Skip empty rows
        $rowArray = is_array($row) ? $row : $row->toArray();
        if (empty(array_filter($rowArray))) {
            return;
        }

        // Clean and validate data
        $data = [
            'nik' => trim($row['nik'] ?? ''),
            'nama_lengkap' => trim($row['nama_lengkap'] ?? ''),
            'email' => trim($row['email'] ?? ''),
            'username' => trim($row['username'] ?? ''),
            'tempat_lahir' => trim($row['tempat_lahir'] ?? ''),
            'tanggal_lahir' => $this->parseDate($row['tanggal_lahir'] ?? ''),
            'jenis_kelamin' => $this->normalizeGender($row['jenis_kelamin'] ?? ''),
            'alamat' => trim($row['alamat'] ?? ''),
            'wilayah' => trim($row['wilayah'] ?? ''),
            'no_telepon' => trim($row['no_telepon'] ?? ''),
            'nama_ayah' => trim($row['nama_ayah'] ?? ''),
            'nama_ibu' => trim($row['nama_ibu'] ?? ''),
            'pekerjaan' => trim($row['pekerjaan'] ?? ''),
            'instansi_asal' => trim($row['instansi_asal'] ?? ''),
            'registration_type' => $this->normalizeRegistrationType($row['registration_type'] ?? 'self'),
            'status_peserta' => $this->normalizeStatus($row['status_peserta'] ?? 'draft'),
            'nama_rekening' => trim($row['nama_rekening'] ?? ''),
            'no_rekening' => trim($row['no_rekening'] ?? '')
        ];

        // Validate required fields
        if (empty($data['nik']) || empty($data['nama_lengkap']) || empty($data['email']) || empty($data['username'])) {
            throw new \Exception('NIK, nama lengkap, email, dan username wajib diisi');
        }

        // Validate NIK format
        if (strlen($data['nik']) !== 16 || !ctype_digit($data['nik'])) {
            throw new \Exception('NIK harus 16 digit angka');
        }

        // Validate email format
        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            throw new \Exception('Format email tidak valid');
        }

        // Find wilayah ID
        if (!isset($this->wilayahCache[$data['wilayah']])) {
            throw new \Exception("Wilayah '{$data['wilayah']}' tidak ditemukan");
        }
        $wilayahId = $this->wilayahCache[$data['wilayah']];

        // Role-based wilayah validation
        if ($this->userRole === 'admin_daerah' && $this->userWilayah && $wilayahId != $this->userWilayah) {
            throw new \Exception("Admin daerah hanya dapat mengimpor peserta untuk wilayahnya sendiri");
        }

        // Check if user already exists
        $existingUser = User::where('email', $data['email'])
                           ->orWhere('username', $data['username'])
                           ->first();

        // Check if peserta with NIK already exists
        $existingPeserta = Peserta::where('nik', $data['nik'])->first();

        DB::transaction(function () use ($data, $wilayahId, $existingUser, $existingPeserta) {
            if ($existingPeserta) {
                // Update existing peserta
                $this->updateExistingPeserta($existingPeserta, $data, $wilayahId);
                $this->importResults['updated'][] = $data['nik'];
            } else {
                // Create new peserta
                $this->createNewPeserta($data, $wilayahId, $existingUser);
                $this->importResults['created'][] = $data['nik'];
            }
        });

        $this->importResults['success']++;
    }

    protected function createNewPeserta($data, $wilayahId, $existingUser)
    {
        // Create or use existing user
        if ($existingUser) {
            $user = $existingUser;
        } else {
            $user = User::create([
                'username' => $data['username'],
                'email' => $data['email'],
                'password' => Hash::make('password123'), // Default password
                'role' => 'peserta'
            ]);
        }

        // Create peserta
        Peserta::create([
            'id_user' => $user->id_user,
            'nik' => $data['nik'],
            'nama_lengkap' => $data['nama_lengkap'],
            'tempat_lahir' => $data['tempat_lahir'],
            'tanggal_lahir' => $data['tanggal_lahir'],
            'jenis_kelamin' => $data['jenis_kelamin'],
            'alamat' => $data['alamat'],
            'id_wilayah' => $wilayahId,
            'no_telepon' => $data['no_telepon'] ?: null,
            'email' => $data['email'],
            'nama_ayah' => $data['nama_ayah'] ?: null,
            'nama_ibu' => $data['nama_ibu'] ?: null,
            'pekerjaan' => $data['pekerjaan'] ?: null,
            'instansi_asal' => $data['instansi_asal'] ?: null,
            'registration_type' => $data['registration_type'],
            'status_peserta' => $data['status_peserta'],
            'nama_rekening' => $data['nama_rekening'] ?: null,
            'no_rekening' => $data['no_rekening'] ?: null,
            'registered_by' => $this->registeredBy,
            'regional_verification_status' => $this->getDefaultVerificationStatus(),
            'regional_verified_by' => $this->getDefaultVerifiedBy(),
            'regional_verified_at' => $this->getDefaultVerifiedAt(),
            'regional_verification_notes' => $this->getDefaultVerificationNotes()
        ]);
    }

    protected function updateExistingPeserta($peserta, $data, $wilayahId)
    {
        $peserta->update([
            'nama_lengkap' => $data['nama_lengkap'],
            'tempat_lahir' => $data['tempat_lahir'],
            'tanggal_lahir' => $data['tanggal_lahir'],
            'jenis_kelamin' => $data['jenis_kelamin'],
            'alamat' => $data['alamat'],
            'id_wilayah' => $wilayahId,
            'no_telepon' => $data['no_telepon'] ?: null,
            'email' => $data['email'],
            'nama_ayah' => $data['nama_ayah'] ?: null,
            'nama_ibu' => $data['nama_ibu'] ?: null,
            'pekerjaan' => $data['pekerjaan'] ?: null,
            'instansi_asal' => $data['instansi_asal'] ?: null,
            'nama_rekening' => $data['nama_rekening'] ?: null,
            'no_rekening' => $data['no_rekening'] ?: null,
        ]);

        // Update user email if changed
        if ($peserta->user && $peserta->user->email !== $data['email']) {
            $peserta->user->update(['email' => $data['email']]);
        }
    }

    protected function parseDate($dateString)
    {
        if (empty($dateString)) {
            throw new \Exception('Tanggal lahir wajib diisi');
        }

        // Try different date formats
        $formats = ['d/m/Y', 'Y-m-d', 'd-m-Y', 'm/d/Y'];
        
        foreach ($formats as $format) {
            $date = \DateTime::createFromFormat($format, $dateString);
            if ($date && $date->format($format) === $dateString) {
                return $date->format('Y-m-d');
            }
        }

        throw new \Exception("Format tanggal tidak valid: {$dateString}. Gunakan format DD/MM/YYYY");
    }

    protected function normalizeGender($gender)
    {
        $gender = strtolower(trim($gender));

        if (empty($gender)) {
            throw new \Exception("Jenis kelamin wajib diisi");
        }

        if (in_array($gender, ['l', 'laki-laki', 'male', 'pria', 'laki', 'putra'])) {
            return 'L';
        } elseif (in_array($gender, ['p', 'perempuan', 'female', 'wanita', 'putri'])) {
            return 'P';
        }

        throw new \Exception("Jenis kelamin tidak valid: '{$gender}'. Gunakan 'Laki-laki' atau 'Perempuan'");
    }

    protected function normalizeRegistrationType($type)
    {
        $type = strtolower(trim($type));
        
        if (in_array($type, ['self', 'daftar sendiri'])) {
            return 'self';
        } elseif (in_array($type, ['admin', 'didaftarkan admin'])) {
            return 'admin';
        } elseif (in_array($type, ['admin_daerah', 'didaftarkan admin daerah'])) {
            return 'admin_daerah';
        }
        
        return 'self'; // Default
    }

    protected function normalizeStatus($status)
    {
        $status = strtolower(trim($status));
        
        $validStatuses = ['draft', 'submitted', 'verified', 'approved', 'rejected'];
        
        if (in_array($status, $validStatuses)) {
            return $status;
        }
        
        return 'draft'; // Default
    }

    protected function getDefaultVerificationStatus()
    {
        return $this->userRole === 'admin_daerah' ? 'pending' : 'verified';
    }

    protected function getDefaultVerifiedBy()
    {
        return $this->userRole === 'admin_daerah' ? null : $this->registeredBy;
    }

    protected function getDefaultVerifiedAt()
    {
        return $this->userRole === 'admin_daerah' ? null : now();
    }

    protected function getDefaultVerificationNotes()
    {
        return $this->userRole === 'admin_daerah' ? null : 'Diimpor oleh admin provinsi';
    }

    /**
     * Get import results
     */
    public function getImportResults()
    {
        return $this->importResults;
    }

    /**
     * @param Failure[] $failures
     */
    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->importResults['failed']++;
            $this->importResults['errors'][] = [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(),
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ];
        }
    }
}
