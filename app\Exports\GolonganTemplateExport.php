<?php

namespace App\Exports;

use App\Models\CabangLomba;
use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class GolonganTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    /**
     * @return array
     */
    public function array(): array
    {
        // Get some sample cabang lomba names for examples
        $cabangLomba = CabangLomba::limit(3)->pluck('nama_cabang')->toArray();
        
        return [
            [
                'TIL-AP',
                'Tilawah Anak-anak Putra',
                $cabangLomba[0] ?? 'Tilawah',
                'Laki-laki',
                6,
                12,
                50,
                100000,
                1,
                50,
                'aktif'
            ],
            [
                'TIL-API',
                'Tilawah <PERSON>k-anak <PERSON>',
                $cabangLomba[0] ?? 'Tilawah',
                'Perempuan',
                6,
                12,
                50,
                100000,
                51,
                100,
                'aktif'
            ],
            [
                'HFZ-RP',
                'Hifzil Quran Remaja Putra',
                $cabangLomba[1] ?? 'Hifzil Quran',
                'Laki-laki',
                13,
                16,
                30,
                150000,
                101,
                130,
                'aktif'
            ]
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'kode_golongan',
            'nama_golongan',
            'cabang_lomba',
            'jenis_kelamin',
            'batas_umur_min',
            'batas_umur_max',
            'kuota_maksimal',
            'biaya_pendaftaran',
            'nomor_urut_awal',
            'nomor_urut_akhir',
            'status'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        // Add instructions in the first few rows
        $sheet->insertNewRowBefore(1, 8);
        
        $sheet->setCellValue('A1', 'TEMPLATE IMPORT GOLONGAN LOMBA');
        $sheet->setCellValue('A2', 'Petunjuk Penggunaan:');
        $sheet->setCellValue('A3', '1. Isi data mulai dari baris 10 (setelah header)');
        $sheet->setCellValue('A4', '2. Kolom cabang_lomba: Harus sesuai dengan nama cabang yang sudah ada');
        $sheet->setCellValue('A5', '3. Kolom jenis_kelamin: "Laki-laki" atau "Perempuan"');
        $sheet->setCellValue('A6', '4. Kolom batas_umur_min/max: Angka dalam tahun');
        $sheet->setCellValue('A7', '5. Kolom biaya_pendaftaran: Angka tanpa titik/koma');
        $sheet->setCellValue('A8', '6. Kolom nomor_urut: Boleh kosong, akan diisi otomatis');

        return [
            // Title style
            1 => [
                'font' => ['bold' => true, 'size' => 14],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            // Instructions style
            '2:8' => [
                'font' => ['italic' => true, 'size' => 10],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFFEF3C7']
                ]
            ],
            // Header style
            9 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }
}
