<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromArray;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class CabangLombaTemplateExport implements FromArray, WithHeadings, WithStyles, ShouldAutoSize
{
    /**
     * @return array
     */
    public function array(): array
    {
        return [
            [
                'TIL',
                'Tilawah',
                'Lomba membaca Al-Quran dengan tartil, tajwid yang benar, dan lagu yang indah',
                'aktif'
            ],
            [
                'HFZ',
                'Hifzil Quran',
                'Lomba menghafal Al-Quran dengan bacaan yang fasih dan benar',
                'aktif'
            ],
            [
                'FHM',
                '<PERSON><PERSON><PERSON><PERSON>',
                '<PERSON>mba pemahaman dan penguasaan isi kandungan Al-Quran',
                'aktif'
            ]
        ];
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'kode_cabang',
            'nama_cabang',
            'deskripsi',
            'status'
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        // Add instructions in the first few rows
        $sheet->insertNewRowBefore(1, 5);
        
        $sheet->setCellValue('A1', 'TEMPLATE IMPORT CABANG LOMBA');
        $sheet->setCellValue('A2', 'Petunjuk Penggunaan:');
        $sheet->setCellValue('A3', '1. Isi data mulai dari baris 7 (setelah header)');
        $sheet->setCellValue('A4', '2. Kolom kode_cabang: Maksimal 10 karakter, harus unik');
        $sheet->setCellValue('A5', '3. Kolom nama_cabang: Maksimal 100 karakter, wajib diisi');
        $sheet->setCellValue('A6', '4. Kolom status: "aktif" atau "non_aktif"');

        return [
            // Title style
            1 => [
                'font' => ['bold' => true, 'size' => 14],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER]
            ],
            // Instructions style
            '2:6' => [
                'font' => ['italic' => true, 'size' => 10],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFFEF3C7']
                ]
            ],
            // Header style
            7 => [
                'font' => ['bold' => true],
                'alignment' => ['horizontal' => Alignment::HORIZONTAL_CENTER],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }
}
