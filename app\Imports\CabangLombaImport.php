<?php

namespace App\Imports;

use App\Models\CabangLomba;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Validator;
use Illuminate\Validation\Rule;
use Maatwebsite\Excel\Concerns\ToCollection;
use Maatwebsite\Excel\Concerns\WithHeadingRow;
use Maatwebsite\Excel\Concerns\WithValidation;
use Maatwebsite\Excel\Concerns\SkipsOnFailure;
use Maatwebsite\Excel\Concerns\SkipsFailures;
use Maatwebsite\Excel\Validators\Failure;

class CabangLombaImport implements ToCollection, WithHeadingRow, WithValidation, SkipsOnFailure
{
    use SkipsFailures;

    protected $importResults = [
        'success' => 0,
        'failed' => 0,
        'errors' => [],
        'created' => [],
        'updated' => []
    ];

    /**
     * @param Collection $collection
     */
    public function collection(Collection $collection)
    {
        foreach ($collection as $row) {
            try {
                $this->processRow($row);
            } catch (\Exception $e) {
                $this->importResults['failed']++;
                $this->importResults['errors'][] = [
                    'row' => $row->toArray(),
                    'error' => $e->getMessage()
                ];
            }
        }
    }

    protected function processRow($row)
    {
        // Clean and validate data
        $data = [
            'kode_cabang' => trim($row['kode_cabang'] ?? ''),
            'nama_cabang' => trim($row['nama_cabang'] ?? ''),
            'deskripsi' => trim($row['deskripsi'] ?? ''),
            'status' => $this->normalizeStatus($row['status'] ?? 'aktif')
        ];

        // Validate required fields
        if (empty($data['kode_cabang']) || empty($data['nama_cabang'])) {
            throw new \Exception('Kode cabang dan nama cabang wajib diisi');
        }

        // Check if cabang already exists
        $existingCabang = CabangLomba::where('kode_cabang', $data['kode_cabang'])->first();

        if ($existingCabang) {
            // Update existing
            $existingCabang->update($data);
            $this->importResults['updated'][] = $data['kode_cabang'];
        } else {
            // Create new
            CabangLomba::create($data);
            $this->importResults['created'][] = $data['kode_cabang'];
        }

        $this->importResults['success']++;
    }

    protected function normalizeStatus($status)
    {
        $status = strtolower(trim($status));
        
        if (in_array($status, ['aktif', 'active', '1', 'ya', 'yes'])) {
            return 'aktif';
        }
        
        return 'non_aktif';
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            'kode_cabang' => 'required|string|max:10',
            'nama_cabang' => 'required|string|max:100',
            'deskripsi' => 'nullable|string',
            'status' => 'nullable|string'
        ];
    }

    /**
     * @return array
     */
    public function customValidationMessages()
    {
        return [
            'kode_cabang.required' => 'Kode cabang wajib diisi',
            'kode_cabang.max' => 'Kode cabang maksimal 10 karakter',
            'nama_cabang.required' => 'Nama cabang wajib diisi',
            'nama_cabang.max' => 'Nama cabang maksimal 100 karakter'
        ];
    }

    /**
     * Get import results
     */
    public function getImportResults()
    {
        return $this->importResults;
    }

    /**
     * @param Failure[] $failures
     */
    public function onFailure(Failure ...$failures)
    {
        foreach ($failures as $failure) {
            $this->importResults['failed']++;
            $this->importResults['errors'][] = [
                'row' => $failure->row(),
                'attribute' => $failure->attribute(),
                'errors' => $failure->errors(),
                'values' => $failure->values()
            ];
        }
    }
}
