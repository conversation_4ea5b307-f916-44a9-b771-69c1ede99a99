@import 'tailwindcss';

@import 'tw-animate-css';

@source '../../vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php';
@source '../../storage/framework/views/*.php';

@custom-variant dark (&:is(.dark *));

@theme inline {
    --font-sans: Instrument Sans, ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';

    --radius-lg: var(--radius);
    --radius-md: calc(var(--radius) - 2px);
    --radius-sm: calc(var(--radius) - 4px);

    --color-background: var(--background);
    --color-foreground: var(--foreground);

    --color-card: var(--card);
    --color-card-foreground: var(--card-foreground);

    --color-popover: var(--popover);
    --color-popover-foreground: var(--popover-foreground);

    --color-primary: var(--primary);
    --color-primary-foreground: var(--primary-foreground);

    --color-secondary: var(--secondary);
    --color-secondary-foreground: var(--secondary-foreground);

    --color-muted: var(--muted);
    --color-muted-foreground: var(--muted-foreground);

    --color-accent: var(--accent);
    --color-accent-foreground: var(--accent-foreground);

    --color-destructive: var(--destructive);
    --color-destructive-foreground: var(--destructive-foreground);

    --color-border: var(--border);
    --color-input: var(--input);
    --color-ring: var(--ring);

    --color-chart-1: var(--chart-1);
    --color-chart-2: var(--chart-2);
    --color-chart-3: var(--chart-3);
    --color-chart-4: var(--chart-4);
    --color-chart-5: var(--chart-5);

    --color-sidebar: var(--sidebar-background);
    --color-sidebar-foreground: var(--sidebar-foreground);
    --color-sidebar-primary: var(--sidebar-primary);
    --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
    --color-sidebar-accent: var(--sidebar-accent);
    --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
    --color-sidebar-border: var(--sidebar-border);
    --color-sidebar-ring: var(--sidebar-ring);
}

/*
  The default border color has changed to `currentColor` in Tailwind CSS v4,
  so we've added these compatibility styles to make sure everything still
  looks the same as it did with Tailwind CSS v3.

  If we ever want to remove these styles, we need to add an explicit border
  color utility to any element that depends on these defaults.
*/
@layer base {
    *,
    ::after,
    ::before,
    ::backdrop,
    ::file-selector-button {
        border-color: var(--color-gray-200, currentColor);
    }
}

@layer utilities {
    body,
    html {
        --font-sans:
            'Instrument Sans', ui-sans-serif, system-ui, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
    }
}

:root {
    /* Islamic Green Theme - Light Mode */
    --background: hsl(0 0% 96%);
    --foreground: hsl(120 10% 15%);
    --card: hsl(0, 0%, 100%);
    --card-foreground: hsl(120 10% 15%);
    --popover: hsl(120 50% 98%);
    --popover-foreground: hsl(120 10% 15%);
    --primary: hsl(142 76% 36%); /* Islamic Green */
    --primary-foreground: hsl(0 0% 98%);
    --secondary: hsl(120 30% 95%);
    --secondary-foreground: hsl(142 76% 36%);
    --muted: hsl(120 20% 96%);
    --muted-foreground: hsl(120 10% 45%);
    --accent: hsl(142 50% 92%);
    --accent-foreground: hsl(142 76% 36%);
    --destructive: hsl(0 84.2% 60.2%);
    --destructive-foreground: hsl(0 0% 98%);
    --border: hsl(120 20% 90%);
    --input: hsl(120 20% 92%);
    --ring: hsl(142 76% 36%);
    --chart-1: hsl(142 76% 36%); /* Islamic Green */
    --chart-2: hsl(160 84% 39%); /* Emerald */
    --chart-3: hsl(174 72% 56%); /* Jade */
    --chart-4: hsl(142 50% 60%); /* Light Green */
    --chart-5: hsl(120 40% 50%); /* Forest Green */
    --radius: 0.5rem;
    --sidebar-background: hsl(142 30% 97%);
    --sidebar-foreground: hsl(120 15% 25%);
    --sidebar-primary: hsl(142 76% 36%);
    --sidebar-primary-foreground: hsl(0 0% 98%);
    --sidebar-accent: hsl(142 40% 94%);
    --sidebar-accent-foreground: hsl(142 76% 36%);
    --sidebar-border: hsl(120 20% 88%);
    --sidebar-ring: hsl(142 76% 36%);
    --sidebar: hsl(142 30% 97%);
}

.dark {
    /* Islamic Green Theme - Dark Mode */
    --background: hsl(120 20% 8%);
    --foreground: hsl(120 20% 95%);
    --card: hsl(120 15% 10%);
    --card-foreground: hsl(120 20% 95%);
    --popover: hsl(120 15% 10%);
    --popover-foreground: hsl(120 20% 95%);
    --primary: hsl(142 76% 45%); /* Brighter Islamic Green for dark mode */
    --primary-foreground: hsl(120 20% 8%);
    --secondary: hsl(120 15% 18%);
    --secondary-foreground: hsl(142 76% 45%);
    --muted: hsl(120 10% 15%);
    --muted-foreground: hsl(120 15% 65%);
    --accent: hsl(142 40% 20%);
    --accent-foreground: hsl(142 76% 45%);
    --destructive: hsl(0 84% 60%);
    --destructive-foreground: hsl(0 0% 98%);
    --border: hsl(120 15% 18%);
    --input: hsl(120 15% 18%);
    --ring: hsl(142 76% 45%);
    --chart-1: hsl(142 76% 45%); /* Islamic Green */
    --chart-2: hsl(160 84% 50%); /* Emerald */
    --chart-3: hsl(174 72% 60%); /* Jade */
    --chart-4: hsl(142 50% 65%); /* Light Green */
    --chart-5: hsl(120 40% 55%); /* Forest Green */
    --sidebar-background: hsl(120 20% 12%);
    --sidebar-foreground: hsl(120 20% 90%);
    --sidebar-primary: hsl(142 76% 45%);
    --sidebar-primary-foreground: hsl(120 20% 8%);
    --sidebar-accent: hsl(120 15% 20%);
    --sidebar-accent-foreground: hsl(142 76% 45%);
    --sidebar-border: hsl(120 15% 18%);
    --sidebar-ring: hsl(142 76% 45%);
    --sidebar: hsl(120 20% 12%);
}

@layer base {
    * {
        @apply border-border outline-ring/50;
    }
    body {
        @apply bg-background text-foreground;
    }
}

/* Islamic-themed utility classes */
@layer utilities {
    .islamic-gradient {
        background: linear-gradient(135deg, hsl(142 76% 36%) 0%, hsl(160 84% 39%) 50%, hsl(174 72% 56%) 100%);
    }

    .islamic-gradient-dark {
        background: linear-gradient(135deg, hsl(142 76% 25%) 0%, hsl(160 84% 30%) 50%, hsl(174 72% 35%) 100%);
    }

    .islamic-pattern {
        background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%2316a34a" fill-opacity="0.05"%3E%3Cpath d="M30 30c0-11.046-8.954-20-20-20s-20 8.954-20 20 8.954 20 20 20 20-8.954 20-20zm0 0c0 11.046 8.954 20 20 20s20-8.954 20-20-8.954-20-20-20-20 8.954-20 20z"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
    }

    .geometric-pattern {
        background-image: url('data:image/svg+xml,%3Csvg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="%2316a34a" fill-opacity="0.03" fill-rule="evenodd"%3E%3Cpath d="M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z"/%3E%3C/g%3E%3C/svg%3E');
    }

    .islamic-border {
        border: 2px solid hsl(142 76% 36%);
        border-radius: 8px;
        position: relative;
    }

    .islamic-border::before {
        content: '';
        position: absolute;
        top: -4px;
        left: -4px;
        right: -4px;
        bottom: -4px;
        background: linear-gradient(45deg, hsl(142 76% 36%), hsl(160 84% 39%), hsl(174 72% 56%));
        border-radius: 12px;
        z-index: -1;
        opacity: 0.1;
    }

    .islamic-shadow {
        box-shadow: 0 4px 20px rgba(34, 197, 94, 0.15);
    }

    .islamic-shadow-lg {
        box-shadow: 0 10px 40px rgba(34, 197, 94, 0.2);
    }
}
