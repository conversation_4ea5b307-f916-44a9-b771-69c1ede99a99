<?php

namespace App\Exports;

use App\Models\CabangLomba;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class CabangLombaExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = CabangLomba::withCount('golongan');

        // Apply filters
        if (!empty($this->filters['search'])) {
            $search = $this->filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('nama_cabang', 'like', "%{$search}%")
                  ->orWhere('kode_cabang', 'like', "%{$search}%");
            });
        }

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        return $query->orderBy('kode_cabang')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Kode Cabang',
            'Nama Cabang',
            'Deskripsi',
            'Status',
            'Jumlah Golongan',
            'Tanggal Dibuat',
            'Tanggal Diperbarui'
        ];
    }

    /**
     * @param mixed $cabang
     * @return array
     */
    public function map($cabang): array
    {
        return [
            $cabang->kode_cabang,
            $cabang->nama_cabang,
            $cabang->deskripsi ?? '',
            $cabang->status === 'aktif' ? 'Aktif' : 'Non Aktif',
            $cabang->golongan_count,
            $cabang->created_at->format('d/m/Y H:i'),
            $cabang->updated_at->format('d/m/Y H:i')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }
}
