<script setup lang="ts">
import { ref } from 'vue'
import { router } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import Card from '@/components/ui/card/Card.vue'
import CardContent from '@/components/ui/card/CardContent.vue'
import CardHeader from '@/components/ui/card/CardHeader.vue'
import CardTitle from '@/components/ui/card/CardTitle.vue'
import Input from '@/components/ui/input/Input.vue'
import Label from '@/components/ui/label/Label.vue'
import Badge from '@/components/ui/badge/Badge.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'
import Select from '@/components/ui/select/Select.vue'
import SelectContent from '@/components/ui/select/SelectContent.vue'
import SelectItem from '@/components/ui/select/SelectItem.vue'
import SelectTrigger from '@/components/ui/select/SelectTrigger.vue'
import SelectValue from '@/components/ui/select/SelectValue.vue'
import Avatar from '@/components/ui/avatar/Avatar.vue'
import AvatarFallback from '@/components/ui/avatar/AvatarFallback.vue'
// dropdown
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from '@/components/ui/dropdown-menu'
import ExportModal from '@/components/ExportModal.vue'
import ImportModal from '@/components/ImportModal.vue'
import { type BreadcrumbItem } from '@/types';

interface Peserta {
    id_peserta: number
    nik: string
    nama_lengkap: string
    email: string
    status_peserta: string
    created_at: string
    wilayah: {
        nama_wilayah: string
    }
}

interface PaginatedPeserta {
    data: Peserta[]
    links: Array<{
        url: string | null
        label: string
        active: boolean
    }>
}

interface Wilayah {
    id_wilayah: number
    nama_wilayah: string
}

const props = defineProps<{
    peserta: PaginatedPeserta
    wilayah: Wilayah[]
    filters: {
        search?: string
        status?: string
        wilayah?: string
    },
    stats: {
        total_peserta: number
        total_peserta_approved: number
        total_peserta_submitted: number
        total_peserta_rejected: number
    }
}>()

const form = ref({
    search: props.filters.search || '',
    status: props.filters.status || 'all',
    wilayah: props.filters.wilayah || 'all'
})

// Export/Import functionality
const showExportModal = ref(false)
const showImportModal = ref(false)

const handleImportSuccess = () => {
    // Refresh the page data
    router.reload()
}

function search() {
    router.get(route('admin.peserta.index'), form.value, {
        preserveState: true,
        replace: true
    })
}

function getInitials(name: string): string {
    return name
        .split(' ')
        .map(word => word.charAt(0))
        .join('')
        .toUpperCase()
        .slice(0, 2)
}

function getStatusClass(status: string): string {
    const classes = {
        draft: 'bg-gray-100 text-gray-800',
        submitted: 'bg-blue-100 text-blue-800',
        verified: 'bg-indigo-100 text-indigo-800',
        approved: 'bg-green-100 text-green-800',
        rejected: 'bg-red-100 text-red-800'
    }
    return classes[status as keyof typeof classes] || 'bg-gray-100 text-gray-800'
}

function getStatusText(status: string): string {
    const texts = {
        draft: 'Draft',
        submitted: 'Disubmit',
        verified: 'Diverifikasi',
        approved: 'Disetujui',
        rejected: 'Ditolak'
    }
    return texts[status as keyof typeof texts] || status
}

function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString('id-ID', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    })
}

function confirmDelete(peserta: Peserta) {
    if (confirm(`Apakah Anda yakin ingin menghapus peserta ${peserta.nama_lengkap}?`)) {
        router.delete(route('admin.peserta.destroy', peserta.id_peserta))
    }
}

function getStatusCount(status: string): number {
    return props.peserta.data.filter(p => p.status_peserta === status).length
}
function getStatusVariant(status: string): string {
    const variants = {
        draft: 'secondary',
        submitted: 'secondary',
        verified: 'secondary',
        approved: 'default',
        rejected: 'destructive',
    }
    return variants[status as keyof typeof variants] || 'secondary'
}
function changeStatus(peserta: Peserta, status: string) {
    if (confirm(`Apakah Anda yakin ingin mengubah status peserta ${peserta.nama_lengkap} menjadi ${getStatusText(status)}?`)) {
        router.put(route('admin.peserta.update', peserta.id_peserta), { status_peserta: status, tipe: 'update_status' })
    }
}

const breadcrumbItems: BreadcrumbItem[] = [
    {
        title: 'Manajemen Peserta',
        href: '/admin/peserta',
    },
];
</script>
<template>
    <AppLayout :breadcrumbs="breadcrumbItems">
        <Head title="Manajemen Peserta" />
        <div class="space-y-6">
            <!-- Header Section -->
            <!-- <div class="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <Heading title="Manajemen Peserta" description="Kelola data peserta dan status pendaftaran" />
        </div>
        <Button as-child>
          <TextLink :href="route('admin.peserta.create')">
            <Icon name="plus" class="w-4 h-4 mr-2" />
            Tambah Peserta
          </TextLink>
        </Button>
      </div> -->

            <!-- Stats Cards -->
            <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <!-- Card total peserta -->
                <Card class="bg-muted">
                    <CardContent class="p-2">
                        <div class="flex items-center">
                            <div class="p-2 bg-blue-100 rounded-lg">
                                <Icon name="users" class="w-5 h-5 text-blue-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Total Peserta</p>
                                <p class="text-2xl font-bold">{{ stats.total_peserta }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card class="bg-muted">
                    <CardContent class="p-2">
                        <div class="flex items-center">
                            <div class="p-2 bg-green-100 rounded-lg">
                                <Icon name="checkCircle" class="w-5 h-5 text-green-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Disetujui</p>
                                <p class="text-2xl font-bold">{{ stats.total_peserta_approved }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card class="bg-muted">
                    <CardContent class="p-2">
                        <div class="flex items-center">
                            <div class="p-2 bg-yellow-100 rounded-lg">
                                <Icon name="clock" class="w-5 h-5 text-yellow-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Menunggu</p>
                                <p class="text-2xl font-bold">{{ stats.total_peserta_submitted }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
                <Card class="bg-muted">
                    <CardContent class="p-2">
                        <div class="flex items-center">
                            <div class="p-2 bg-red-100 rounded-lg">
                                <Icon name="xCircle" class="w-5 h-5 text-red-600" />
                            </div>
                            <div class="ml-4">
                                <p class="text-sm font-medium text-muted-foreground">Ditolak</p>
                                <p class="text-2xl font-bold">{{ stats.total_peserta_rejected }}</p>
                            </div>
                        </div>
                    </CardContent>
                </Card>
            </div>

            <!-- Enhanced Filters -->
            <Card class="pb-3">
                <CardHeader>
                    <CardTitle class="flex items-center gap-2">
                        <Icon name="filter" class="w-4 h-4" />
                        Filter & Pencarian
                    </CardTitle>
                </CardHeader>
                <CardContent class="space-y-4">
                    <form @submit.prevent="search" class="space-y-2">
                        <div class="grid grid-cols-1 md:grid-cols-4 gap-2">
                            <div class="space-y-2">
                                <Label for="search">Pencarian</Label>
                                <div class="relative">
                                    <Icon name="search" class="absolute left-3 top-3 w-4 h-4 text-muted-foreground" />
                                    <Input id="search" v-model="form.search" placeholder="Nama, NIK, atau Email..."
                                        class="pl-9" />
                                </div>
                            </div>
                            <div class="space-y-2">
                                <Label for="status">Status</Label>
                                <Select v-model="form.status">
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Semua Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Status</SelectItem>
                                        <SelectItem value="draft">Draft</SelectItem>
                                        <SelectItem value="submitted">Disubmit</SelectItem>
                                        <SelectItem value="verified">Diverifikasi</SelectItem>
                                        <SelectItem value="approved">Disetujui</SelectItem>
                                        <SelectItem value="rejected">Ditolak</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                            <div class="space-y-2">
                                <Label for="wilayah">Wilayah</Label>
                                <Select v-model="form.wilayah">
                                    <SelectTrigger class="w-full">
                                        <SelectValue placeholder="Semua Wilayah" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="all">Semua Wilayah</SelectItem>
                                        <SelectItem v-for="w in wilayah" :key="w.id_wilayah"
                                            :value="w.id_wilayah.toString()">
                                            {{ w.nama_wilayah }}
                                        </SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                        <div class="flex flex-col sm:flex-row gap-2 sm:justify-between">
                            <div class="flex gap-2">
                                <Button type="submit" variant="secondary">
                                    <Icon name="search" class="w-4 h-4 mr-2" />
                                    Cari
                                </Button>
                                <Button type="button" variant="outline" @click="resetFilters">
                                    <Icon name="x" class="w-4 h-4 mr-2" />
                                    Reset
                                </Button>
                            </div>
                            <div class="flex gap-2">
                                <DropdownMenu>
                                    <DropdownMenuTrigger as-child>
                                        <Button variant="outline" size="sm">
                                            <Icon name="download" class="w-4 h-4 mr-2" />
                                            Export/Import
                                            <Icon name="chevron-down" class="w-4 h-4 ml-2" />
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent align="end">
                                        <DropdownMenuItem @click="showExportModal = true">
                                            <Icon name="download" class="w-4 h-4 mr-2" />
                                            Export Data
                                        </DropdownMenuItem>
                                        <DropdownMenuItem @click="showImportModal = true">
                                            <Icon name="upload" class="w-4 h-4 mr-2" />
                                            Import Data
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            </div>
                        </div>
                    </form>
                </CardContent>
            </Card>

            <!-- Enhanced Table -->
            <Card class="gap-0">
                <CardHeader>
                    <div class="flex justify-between items-center">
                        <CardTitle>Daftar Peserta</CardTitle>
                        <div class="flex items-center gap-2 text-sm text-muted-foreground">
                            <span>{{ peserta.data.length }} dari {{ peserta.total || 0 }} peserta</span>
                        </div>
                        <Button as-child>
                            <TextLink :href="route('admin.peserta.create')">
                                <Icon name="plus" class="w-4 h-4 mr-2" />
                                Tambah Peserta
                            </TextLink>
                        </Button>
                    </div>
                </CardHeader>
                <CardContent class="p-0">
                    <div class="overflow-x-auto">
                        <table class="w-full">
                            <thead>
                                <tr class="border-b bg-slate-50">
                                    <th class="text-left p-4 font-medium">Peserta</th>
                                    <th class="text-left p-4 font-medium">NIK</th>
                                    <th class="text-left p-4 font-medium">Wilayah</th>
                                    <th class="text-left p-4 font-medium">Status</th>
                                    <th class="text-left p-4 font-medium">Terdaftar</th>
                                    <th class="text-right p-4 font-medium">Aksi</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr v-for="p in peserta.data" :key="p.id_peserta"
                                    class="border-b hover:bg-white transition-colors bg-slate-100">
                                    <td class="py-1 px-3">
                                        <div class="flex items-center gap-3">
                                            <Avatar class="h-8 w-8 shadow">
                                                <AvatarFallback>{{ getInitials(p.nama_lengkap) }}</AvatarFallback>
                                            </Avatar>
                                            <div>
                                                <div class="font-medium">{{ p.nama_lengkap }}</div>
                                                <div class="text-sm text-muted-foreground">{{ p.email }}</div>
                                            </div>
                                        </div>
                                    </td>
                                    <td class=" font-mono text-sm">
                                        {{ p.nik }}
                                    </td>
                                    <td class="">
                                        <div class="flex items-center gap-2">
                                            <Icon name="map-pin" class="w-4 h-4 text-muted-foreground" />
                                            {{ p.wilayah.nama_wilayah }}
                                        </div>
                                    </td>
                                    <td class="">
                                        <Badge :variant="getStatusVariant(p.status_peserta)">
                                            {{ getStatusText(p.status_peserta) }}
                                        </Badge>
                                    </td>
                                    <td class=" text-sm text-muted-foreground">
                                        {{ formatDate(p.created_at) }}
                                    </td>
                                    <td class="px-2">
                                        <div class="flex justify-end gap-1">
                                            <Button variant="outline" size="sm" as-child>
                                                <TextLink :href="route('admin.peserta.show', p.id_peserta)">
                                                    <Icon name="eye" class="w-3 h-3" />
                                                </TextLink>
                                            </Button>
                                            <Button variant="outline" size="sm" as-child>
                                                <TextLink :href="route('admin.peserta.edit', p.id_peserta)">
                                                    <Icon name="edit" class="w-3 h-3" />
                                                </TextLink>
                                            </Button>
                                            <DropdownMenu :key="p.id_peserta">
                                                <DropdownMenuTrigger>
                                                    <Button variant="outline" size="sm">
                                                        <Icon name="moreVertical" class="w-3 h-3" />
                                                    </Button>
                                                </DropdownMenuTrigger>
                                                <DropdownMenuContent align="end" class="bg-yellow-50">
                                                    <DropdownMenuItem @click="changeStatus(p, 'approved')">
                                                        <Icon name="check" class="w-3 h-3 mr-2" />
                                                        Setujui
                                                    </DropdownMenuItem>
                                                    <DropdownMenuItem @click="changeStatus(p, 'rejected')">
                                                        <Icon name="x" class="w-3 h-3 mr-2" />
                                                        Tolak
                                                    </DropdownMenuItem>
                                                    <DropdownMenuSeparator />
                                                    <DropdownMenuItem @click="confirmDelete(p)"
                                                        class="text-destructive focus:text-destructive">
                                                        <Icon name="trash-2" class="w-3 h-3 mr-2" />
                                                        Hapus
                                                    </DropdownMenuItem>
                                                </DropdownMenuContent>
                                            </DropdownMenu>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                    <!-- Empty State -->
                    <div v-if="peserta.data.length === 0" class="text-center py-12">
                        <div class="flex flex-col items-center gap-4">
                            <div class="p-4 bg-muted rounded-full">
                                <Icon name="users" class="w-8 h-8 text-muted-foreground" />
                            </div>
                            <div>
                                <h3 class="text-lg font-semibold">Tidak ada peserta ditemukan</h3>
                                <p class="text-sm text-muted-foreground">
                                    Coba ubah filter pencarian atau tambah peserta baru
                                </p>
                            </div>
                            <Button as-child>
                                <TextLink :href="route('admin.peserta.create')">
                                    <Icon name="plus" class="w-4 h-4 mr-2" />
                                    Tambah Peserta
                                </TextLink>
                            </Button>
                        </div>
                    </div>
                </CardContent>
            </Card>


            <!-- Pagination -->
            <div v-if="peserta.links" class="flex justify-center">
                <nav class="flex space-x-2">
                    <div v-for="link in peserta.links" :key="link.label">
                        <Button v-if="link.url" as-child :variant="link.active ? 'primary' : 'ghost'" size="sm">
                            <TextLink :href="link.url">
                                {{ link.label }}
                            </TextLink>
                        </Button>
                        <span v-else class="px-3 py-2 text-sm text-gray-500" v-html="link.label" />
                    </div>
                </nav>
            </div>
        </div>

        <!-- Export Modal -->
        <ExportModal
            v-model:show="showExportModal"
            title="Peserta"
            :export-url="route('admin.peserta.export')"
            :show-filters="true"
        />

        <!-- Import Modal -->
        <ImportModal
            v-model:show="showImportModal"
            title="Peserta"
            :import-url="route('admin.peserta.import')"
            :template-url="route('admin.peserta.template')"
            @imported="handleImportSuccess"
        />
    </AppLayout>
</template>
