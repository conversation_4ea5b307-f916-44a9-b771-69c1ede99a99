<script setup lang="ts">
import { ref } from 'vue'
import { useForm } from '@inertiajs/vue3'
import AppLayout from '@/layouts/AppLayout.vue'
import { Head } from '@inertiajs/vue3'
import Heading from '@/components/Heading.vue'
import Button from '@/components/ui/button/Button.vue'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Checkbox } from '@/components/ui/checkbox'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Alert, AlertDescription } from '@/components/ui/alert'
import InputError from '@/components/InputError.vue'
import Icon from '@/components/Icon.vue'
import TextLink from '@/components/TextLink.vue'

interface CabangLomba {
  nama_cabang: string
  deskripsi: string
}

interface Golongan {
  id_golongan: number
  nama_golongan: string
  jenis_kelamin: string
  batas_umur_min: number
  batas_umur_max: number
  kuota_max: number
  biaya_pendaftaran: number
  cabang_lomba: CabangLomba
}

const props = defineProps<{
  golongan: Golongan[]
  admin_wilayah: number
}>()

const form = useForm({
  username: '',
  nama_lengkap: '',
  email: '',
  password: '',
  password_confirmation: '',
  nik: '',
  tempat_lahir: '',
  tanggal_lahir: '',
  jenis_kelamin: '',
  alamat: '',
  no_telepon: '',
  golongan_ids: [] as number[],
  auto_approve: false,
  keterangan: ''
})

const selectedGolongan = ref<number[]>([])

function toggleGolongan(golonganId: number) {
  const index = selectedGolongan.value.indexOf(golonganId)
  if (index > -1) {
    selectedGolongan.value.splice(index, 1)
  } else {
    selectedGolongan.value.push(golonganId)
  }
  form.golongan_ids = [...selectedGolongan.value]
}

function isEligible(golongan: Golongan): boolean {
  if (!form.jenis_kelamin || !form.tanggal_lahir) return true

  // Check gender
  if (form.jenis_kelamin !== golongan.jenis_kelamin) return false

  // Check age
  const age = calculateAge(form.tanggal_lahir)
  if (age < golongan.batas_umur_min || age > golongan.batas_umur_max) return false

  return true
}

function calculateAge(birthDate: string): number {
  if (!birthDate) return 0

  const today = new Date()
  const birth = new Date(birthDate)
  let age = today.getFullYear() - birth.getFullYear()
  const monthDiff = today.getMonth() - birth.getMonth()

  if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
    age--
  }

  return age
}

function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('id-ID', {
    style: 'currency',
    currency: 'IDR',
    minimumFractionDigits: 0
  }).format(amount)
}

function submit() {
  form.post(route('admin-daerah.peserta.store'))
}

function generatePassword() {
  const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789'
  let password = ''
  for (let i = 0; i < 8; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length))
  }
  form.password = password
  form.password_confirmation = password
}
</script>

<template>
  <AppLayout>
    <template #header>
      <div class="flex items-center space-x-4">
        <Button
          as-child
          variant="ghost"
          size="sm"
        >
          <TextLink :href="route('admin-daerah.peserta.index')">
            <Icon name="arrow-left" class="w-4 h-4 mr-2" />
            Kembali
          </TextLink>
        </Button>
        <Heading title="Daftarkan Peserta Baru" />
      </div>
    </template>

    <Head title="Daftarkan Peserta Baru" />

    <div class="space-y-6">
      <Alert>
        <Icon name="info" class="h-4 w-4" />
        <AlertDescription>
          Sebagai admin daerah, Anda dapat mendaftarkan peserta secara langsung dan memilih untuk menyetujui pendaftaran secara otomatis.
        </AlertDescription>
      </Alert>

      <form @submit.prevent="submit" class="space-y-6">
        <!-- Account Information -->
        <Card>
          <CardHeader>
            <CardTitle>Informasi Akun</CardTitle>
            <CardDescription>Data akun untuk login peserta</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="grid gap-2">
                <Label for="username">Username</Label>
                <Input
                  id="username"
                  v-model="form.username"
                  placeholder="Username untuk login"
                  required
                />
                <InputError :message="form.errors.username" />
              </div>

              <div class="grid gap-2">
                <Label for="email">Email</Label>
                <Input
                  id="email"
                  type="email"
                  v-model="form.email"
                  placeholder="<EMAIL>"
                  required
                />
                <InputError :message="form.errors.email" />
              </div>
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="grid gap-2">
                <Label for="password">Password</Label>
                <div class="flex space-x-2">
                  <Input
                    id="password"
                    type="password"
                    v-model="form.password"
                    placeholder="Password"
                    required
                    class="flex-1"
                  />
                  <Button type="button" variant="outline" @click="generatePassword">
                    <Icon name="refresh-cw" class="w-4 h-4" />
                  </Button>
                </div>
                <InputError :message="form.errors.password" />
              </div>

              <div class="grid gap-2">
                <Label for="password_confirmation">Konfirmasi Password</Label>
                <Input
                  id="password_confirmation"
                  type="password"
                  v-model="form.password_confirmation"
                  placeholder="Konfirmasi password"
                  required
                />
                <InputError :message="form.errors.password_confirmation" />
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Personal Information -->
        <Card>
          <CardHeader>
            <CardTitle>Data Pribadi</CardTitle>
            <CardDescription>Informasi pribadi peserta</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="grid gap-2">
              <Label for="nama_lengkap">Nama Lengkap</Label>
              <Input
                id="nama_lengkap"
                v-model="form.nama_lengkap"
                placeholder="Nama lengkap peserta"
                required
              />
              <InputError :message="form.errors.nama_lengkap" />
            </div>

            <div class="grid gap-2">
              <Label for="nik">NIK</Label>
              <Input
                id="nik"
                v-model="form.nik"
                placeholder="Nomor Induk Kependudukan (16 digit)"
                maxlength="16"
                required
              />
              <InputError :message="form.errors.nik" />
            </div>

            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div class="grid gap-2">
                <Label for="tempat_lahir">Tempat Lahir</Label>
                <Input
                  id="tempat_lahir"
                  v-model="form.tempat_lahir"
                  placeholder="Tempat lahir"
                  required
                />
                <InputError :message="form.errors.tempat_lahir" />
              </div>

              <div class="grid gap-2">
                <Label for="tanggal_lahir">Tanggal Lahir</Label>
                <Input
                  id="tanggal_lahir"
                  type="date"
                  v-model="form.tanggal_lahir"
                  required
                />
                <InputError :message="form.errors.tanggal_lahir" />
              </div>
            </div>

            <div class="grid gap-2">
              <Label for="jenis_kelamin">Jenis Kelamin</Label>
              <select
                id="jenis_kelamin"
                v-model="form.jenis_kelamin"
                required
                class="flex h-9 w-full rounded-md border border-input bg-transparent px-3 py-1 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
              >
                <option value="all">Pilih Jenis Kelamin</option>
                <option value="L">Laki-laki</option>
                <option value="P">Perempuan</option>
              </select>
              <InputError :message="form.errors.jenis_kelamin" />
            </div>

            <div class="grid gap-2">
              <Label for="alamat">Alamat</Label>
              <Textarea
                id="alamat"
                v-model="form.alamat"
                placeholder="Alamat lengkap"
                rows="3"
                required
              />
              <InputError :message="form.errors.alamat" />
            </div>

            <div class="grid gap-2">
              <Label for="no_telepon">No. Telepon (Opsional)</Label>
              <Input
                id="no_telepon"
                type="tel"
                v-model="form.no_telepon"
                placeholder="Nomor telepon"
              />
              <InputError :message="form.errors.no_telepon" />
            </div>
          </CardContent>
        </Card>

        <!-- Competition Registration -->
        <Card>
          <CardHeader>
            <CardTitle>Pendaftaran Lomba (Opsional)</CardTitle>
            <CardDescription>Pilih golongan lomba yang akan diikuti peserta</CardDescription>
          </CardHeader>
          <CardContent>
            <div v-if="!form.jenis_kelamin || !form.tanggal_lahir" class="text-center py-8">
              <Icon name="info" class="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p class="text-gray-600">Lengkapi jenis kelamin dan tanggal lahir untuk melihat golongan yang tersedia.</p>
            </div>

            <div v-else class="grid grid-cols-1 lg:grid-cols-2 gap-4">
              <div
                v-for="g in golongan"
                :key="g.id_golongan"
                class="p-4 border rounded-lg transition-all"
                :class="[
                  isEligible(g) ? 'hover:border-blue-500 cursor-pointer' : 'opacity-50 cursor-not-allowed',
                  selectedGolongan.includes(g.id_golongan) ? 'border-blue-500 bg-blue-50' : ''
                ]"
                @click="isEligible(g) && toggleGolongan(g.id_golongan)"
              >
                <div class="flex items-center space-x-3 mb-2">
                  <Checkbox
                    :checked="selectedGolongan.includes(g.id_golongan)"
                    :disabled="!isEligible(g)"
                    @update:checked="isEligible(g) && toggleGolongan(g.id_golongan)"
                  />
                  <div class="flex-1">
                    <h4 class="font-medium">{{ g.nama_golongan }}</h4>
                    <p class="text-sm text-gray-600">{{ g.cabang_lomba.nama_cabang }}</p>
                  </div>
                  <Badge :class="g.jenis_kelamin === 'L' ? 'bg-blue-100 text-blue-800' : 'bg-pink-100 text-pink-800'">
                    {{ g.jenis_kelamin === 'L' ? 'Putra' : 'Putri' }}
                  </Badge>
                </div>
                <div class="text-sm space-y-1">
                  <p>Usia: {{ g.batas_umur_min }} - {{ g.batas_umur_max }} tahun</p>
                  <p>Kuota: {{ g.kuota_max }} peserta</p>
                  <p class="font-medium text-green-600">{{ formatCurrency(g.biaya_pendaftaran) }}</p>
                </div>
                <div v-if="!isEligible(g)" class="mt-2 text-xs text-red-600">
                  Tidak memenuhi syarat (usia atau jenis kelamin)
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <!-- Additional Options -->
        <Card>
          <CardHeader>
            <CardTitle>Opsi Tambahan</CardTitle>
            <CardDescription>Pengaturan khusus untuk pendaftaran</CardDescription>
          </CardHeader>
          <CardContent class="space-y-4">
            <div class="flex items-center space-x-2">
              <Checkbox
                id="auto_approve"
                v-model="form.auto_approve"
              />
              <Label for="auto_approve" class="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Setujui pendaftaran secara otomatis
              </Label>
            </div>
            <p class="text-xs text-gray-500">
              Jika dicentang, peserta dan pendaftaran lomba akan langsung disetujui tanpa perlu verifikasi manual.
            </p>

            <div class="grid gap-2">
              <Label for="keterangan">Keterangan (Opsional)</Label>
              <Textarea
                id="keterangan"
                v-model="form.keterangan"
                placeholder="Tambahkan catatan atau keterangan khusus..."
                rows="3"
              />
              <InputError :message="form.errors.keterangan" />
            </div>
          </CardContent>
        </Card>

        <!-- Submit Button -->
        <div class="flex justify-end space-x-4 pt-6 border-t">
          <Button
            as-child
            variant="outline"
          >
            <TextLink :href="route('admin-daerah.peserta.index')">
              Batal
            </TextLink>
          </Button>
          <Button
            type="submit"
            :disabled="form.processing"
          >
            <Icon v-if="form.processing" name="loader-2" class="w-4 h-4 mr-2 animate-spin" />
            {{ form.processing ? 'Mendaftarkan...' : 'Daftarkan Peserta' }}
          </Button>
        </div>
      </form>
    </div>
  </AppLayout>
</template>
