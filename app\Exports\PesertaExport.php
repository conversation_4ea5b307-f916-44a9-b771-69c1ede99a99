<?php

namespace App\Exports;

use App\Models\Peserta;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class PesertaExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;
    protected $userRole;
    protected $userWilayah;

    public function __construct(array $filters = [], $userRole = null, $userWilayah = null)
    {
        $this->filters = $filters;
        $this->userRole = $userRole;
        $this->userWilayah = $userWilayah;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Peserta::with(['user', 'wilayah', 'registeredBy', 'pendaftaran.golongan.cabangLomba']);

        // Apply role-based filtering
        if ($this->userRole === 'admin_daerah' && $this->userWilayah) {
            $query->where('id_wilayah', $this->userWilayah);
        }

        // Apply filters
        if (!empty($this->filters['search'])) {
            $search = $this->filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%")
                  ->orWhere('email', 'like', "%{$search}%");
            });
        }

        if (!empty($this->filters['status'])) {
            $query->where('status_peserta', $this->filters['status']);
        }

        if (!empty($this->filters['wilayah'])) {
            $query->where('id_wilayah', $this->filters['wilayah']);
        }

        if (!empty($this->filters['registration_type'])) {
            $query->where('registration_type', $this->filters['registration_type']);
        }

        return $query->orderBy('created_at', 'desc')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'NIK',
            'Nama Lengkap',
            'Email',
            'Username',
            'Tempat Lahir',
            'Tanggal Lahir',
            'Jenis Kelamin',
            'Alamat',
            'Wilayah',
            'No Telepon',
            'Nama Ayah',
            'Nama Ibu',
            'Pekerjaan',
            'Instansi Asal',
            'Tipe Registrasi',
            'Status Peserta',
            'Status Verifikasi Regional',
            'Tanggal Verifikasi Regional',
            'Catatan Verifikasi Regional',
            'Dokumen Lengkap',
            'Nama Rekening',
            'No Rekening',
            'Didaftarkan Oleh',
            'Tanggal Dibuat',
            'Tanggal Diperbarui'
        ];
    }

    /**
     * @param mixed $peserta
     * @return array
     */
    public function map($peserta): array
    {
        return [
            $peserta->nik,
            $peserta->nama_lengkap,
            $peserta->email ?? $peserta->user->email ?? '',
            $peserta->user->username ?? '',
            $peserta->tempat_lahir,
            $peserta->tanggal_lahir ? $peserta->tanggal_lahir->format('d/m/Y') : '',
            $peserta->jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan',
            $peserta->alamat,
            $peserta->wilayah->nama_wilayah ?? '',
            $peserta->no_telepon ?? '',
            $peserta->nama_ayah ?? '',
            $peserta->nama_ibu ?? '',
            $peserta->pekerjaan ?? '',
            $peserta->instansi_asal ?? '',
            $this->getRegistrationTypeText($peserta->registration_type),
            $this->getStatusText($peserta->status_peserta),
            $this->getVerificationStatusText($peserta->regional_verification_status),
            $peserta->regional_verified_at ? $peserta->regional_verified_at->format('d/m/Y H:i') : '',
            $peserta->regional_verification_notes ?? '',
            $peserta->documents_complete ? 'Ya' : 'Tidak',
            $peserta->nama_rekening ?? '',
            $peserta->no_rekening ?? '',
            $peserta->registeredBy->nama_lengkap ?? 'Self Registration',
            $peserta->created_at->format('d/m/Y H:i'),
            $peserta->updated_at->format('d/m/Y H:i')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }

    private function getRegistrationTypeText($type)
    {
        $types = [
            'self' => 'Daftar Sendiri',
            'admin' => 'Didaftarkan Admin',
            'admin_daerah' => 'Didaftarkan Admin Daerah'
        ];

        return $types[$type] ?? $type;
    }

    private function getStatusText($status)
    {
        $statuses = [
            'draft' => 'Draft',
            'submitted' => 'Disubmit',
            'verified' => 'Terverifikasi',
            'approved' => 'Disetujui',
            'rejected' => 'Ditolak'
        ];

        return $statuses[$status] ?? $status;
    }

    private function getVerificationStatusText($status)
    {
        $statuses = [
            'pending' => 'Menunggu',
            'verified' => 'Terverifikasi',
            'rejected' => 'Ditolak'
        ];

        return $statuses[$status] ?? $status;
    }
}
