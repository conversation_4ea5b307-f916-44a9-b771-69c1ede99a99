<?php

namespace App\Exports;

use App\Models\Golongan;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;
use Maatwebsite\Excel\Concerns\WithStyles;
use Maatwebsite\Excel\Concerns\ShouldAutoSize;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;
use PhpOffice\PhpSpreadsheet\Style\Alignment;
use PhpOffice\PhpSpreadsheet\Style\Font;

class GolonganExport implements FromCollection, WithHeadings, WithMapping, WithStyles, ShouldAutoSize
{
    protected $filters;

    public function __construct(array $filters = [])
    {
        $this->filters = $filters;
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $query = Golongan::with('cabangLomba');

        // Apply filters
        if (!empty($this->filters['search'])) {
            $search = $this->filters['search'];
            $query->where(function ($q) use ($search) {
                $q->where('nama_golongan', 'like', "%{$search}%")
                  ->orWhere('kode_golongan', 'like', "%{$search}%")
                  ->orWhereHas('cabangLomba', function ($cabangQuery) use ($search) {
                      $cabangQuery->where('nama_cabang', 'like', "%{$search}%");
                  });
            });
        }

        if (!empty($this->filters['status'])) {
            $query->where('status', $this->filters['status']);
        }

        if (!empty($this->filters['id_cabang'])) {
            $query->where('id_cabang', $this->filters['id_cabang']);
        }

        return $query->orderBy('kode_golongan')->get();
    }

    /**
     * @return array
     */
    public function headings(): array
    {
        return [
            'Kode Golongan',
            'Nama Golongan',
            'Cabang Lomba',
            'Jenis Kelamin',
            'Batas Umur Min',
            'Batas Umur Max',
            'Kuota Maksimal',
            'Biaya Pendaftaran',
            'Nomor Urut Awal',
            'Nomor Urut Akhir',
            'Status',
            'Tanggal Dibuat',
            'Tanggal Diperbarui'
        ];
    }

    /**
     * @param mixed $golongan
     * @return array
     */
    public function map($golongan): array
    {
        return [
            $golongan->kode_golongan,
            $golongan->nama_golongan,
            $golongan->cabangLomba->nama_cabang ?? '',
            $golongan->jenis_kelamin === 'L' ? 'Laki-laki' : 'Perempuan',
            $golongan->batas_umur_min,
            $golongan->batas_umur_max,
            $golongan->kuota_max,
            number_format($golongan->biaya_pendaftaran, 0, ',', '.'),
            $golongan->nomor_urut_awal ?? '',
            $golongan->nomor_urut_akhir ?? '',
            $golongan->status === 'aktif' ? 'Aktif' : 'Non Aktif',
            $golongan->created_at->format('d/m/Y H:i'),
            $golongan->updated_at->format('d/m/Y H:i')
        ];
    }

    /**
     * @param Worksheet $sheet
     * @return array
     */
    public function styles(Worksheet $sheet)
    {
        return [
            // Style the first row as bold
            1 => [
                'font' => ['bold' => true],
                'alignment' => [
                    'horizontal' => Alignment::HORIZONTAL_CENTER,
                ],
                'fill' => [
                    'fillType' => \PhpOffice\PhpSpreadsheet\Style\Fill::FILL_SOLID,
                    'startColor' => ['argb' => 'FFE2E8F0']
                ]
            ],
        ];
    }
}
