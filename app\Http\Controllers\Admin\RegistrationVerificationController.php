<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Pendaftaran;
use App\Models\DokumenPeserta;
use App\Models\ParticipantVerification;
use App\Models\VerificationType;
use App\Models\Wilayah;
use App\Models\Golongan;
use App\Models\CabangLomba;
use App\Services\VerificationService;
use App\Traits\HasVerificationPermissions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Inertia\Inertia;
use Inertia\Response;

class RegistrationVerificationController extends Controller
{
    use HasVerificationPermissions;

    protected VerificationService $verificationService;

    public function __construct(VerificationService $verificationService)
    {
        $this->verificationService = $verificationService;
    }

    /**
     * Display the registration verification dashboard
     */
    public function index(Request $request): Response
    {
        $this->requireVerificationAccess();

        $query = Pendaftaran::with([
            'peserta.user',
            'peserta.wilayah',
            'golongan.cabangLomba',
            'verifiedBy',
            'approvedBy',
            'documents.documentType',
            'participantVerifications.verificationType'
        ]);

        // Apply filters
        $this->applyFilters($query, $request);
        // print $request;
        // print $query->toSql(); die();
        // Apply search
        if ($request->filled('search')) {
            $search = $request->search;
            $query->whereHas('peserta', function ($q) use ($search) {
                $q->where('nama_lengkap', 'like', "%{$search}%")
                  ->orWhere('nik', 'like', "%{$search}%");
            })->orWhere('nomor_pendaftaran', 'like', "%{$search}%")
              ->orWhere('nomor_peserta', 'like', "%{$search}%");
        }

        // Get paginated results
        $registrations = $query->latest('created_at')->paginate(20)->withQueryString();

        // Add verification progress for each registration
        $registrations->getCollection()->transform(function ($registration) {
            $registration->verification_progress = $this->calculateVerificationProgress($registration);
            $registration->document_status = $this->getDocumentStatus($registration);
            $registration->participant_status = $this->getParticipantVerificationStatus($registration);
            return $registration;
        });

        // Get filter options
        $filterOptions = $this->getFilterOptions();

        // Get verification types
        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/RegistrationVerification/Index', [
            'registrations' => $registrations,
            'filters' => $request->only(['status', 'wilayah', 'cabang_lomba', 'golongan', 'search']),
            'filterOptions' => $filterOptions,
            'stats' => $this->getVerificationStats(),
            'verificationTypes' => $verificationTypes
        ]);
    }

    /**
     * Show detailed verification view for a specific registration
     */
    public function show(Pendaftaran $pendaftaran): Response
    {
        $this->requireVerificationAccess();

        $pendaftaran->load([
            'peserta.user',
            'peserta.wilayah',
            'peserta.verifications.verificationType',
            'peserta.verifications.verifiedBy',
            'golongan.cabangLomba',
            'golongan.allDocumentTypes',
            'verifiedBy',
            'approvedBy'
        ]);

        // Get documents for this registration
        $documents = DokumenPeserta::with(['documentType', 'verifiedBy'])
            ->where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->get();

        // Get required documents for this golongan
        // $requiredDocuments = $pendaftaran->golongan->requiredDocumentTypes;
        $requiredDocuments = $pendaftaran->golongan->allDocumentTypes()->get();

        // Get verification types
        $verificationTypes = VerificationType::active()->ordered()->get();

        return Inertia::render('Admin/RegistrationVerification/Show', [
            'registration' => $pendaftaran,
            'documents' => $documents,
            'requiredDocuments' => $requiredDocuments,
            'verificationTypes' => $verificationTypes,
            'verificationProgress' => $this->calculateVerificationProgress($pendaftaran)
        ]);
    }

    /**
     * Verify a registration (approve or reject)
     */
    public function verify(Request $request, Pendaftaran $pendaftaran)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000',
            'use_default_reason' => 'boolean'
        ]);

        $status = $validated['action'] === 'approve'
            ? VerificationService::STATUS_VERIFIED
            : VerificationService::STATUS_REJECTED;

        // Enhanced: Use default reasons if not provided
        $notes = $validated['notes'];
        if (empty($notes) || ($validated['use_default_reason'] ?? false)) {
            $notes = $validated['action'] === 'approve'
                ? 'Data valid dan lengkap'
                : 'Data tidak valid atau lengkap';
        }

        $this->verificationService->verifyRegistration(
            $pendaftaran,
            $status,
            $notes
        );

        $statusText = $this->verificationService->getStatusMessage($status, 'registration');

        return back()->with('success', "Pendaftaran {$statusText}.");
    }

    /**
     * Bulk verify registrations
     */
    public function bulkVerify(Request $request)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'registration_ids' => 'required|array|min:1',
            'registration_ids.*' => 'exists:pendaftaran,id_pendaftaran',
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000',
            'use_default_reason' => 'boolean'
        ]);

        $status = $validated['action'] === 'approve' ? 'verified' : 'rejected';

        // Enhanced: Use default reasons if not provided
        $notes = $validated['notes'];
        if (empty($notes) || ($validated['use_default_reason'] ?? false)) {
            $notes = $validated['action'] === 'approve'
                ? 'Data valid dan lengkap'
                : 'Data tidak valid atau lengkap';
        }

        $count = 0;

        DB::transaction(function () use ($validated, $status, $notes, &$count) {
            $registrations = Pendaftaran::with(['peserta', 'dokumenPeserta'])
                ->whereIn('id_pendaftaran', $validated['registration_ids'])
                ->get();

            $auditData = [];
            $currentTime = now();
            $userId = Auth::id();

            foreach ($registrations as $registration) {
                // Store audit data before update
                $auditData[] = [
                    'registration_id' => $registration->id_pendaftaran,
                    'previous_status' => $registration->status_pendaftaran,
                    'new_status' => $status,
                    'action_type' => 'bulk_verification',
                    'performed_by' => $userId,
                    'performed_at' => $currentTime,
                    'notes' => $notes,
                    'participant_name' => $registration->peserta->nama_lengkap,
                    'participant_nik' => $registration->peserta->nik
                ];

                $registration->update([
                    'status_pendaftaran' => $status,
                    'verified_by' => $userId,
                    'verified_at' => $currentTime,
                    'catatan_verifikasi' => $notes
                ]);

                // Update peserta status as well
                $registration->peserta->update([
                    'status_peserta' => $status
                ]);

                // update dokumen status
                $registration->dokumenPeserta()->update([
                    'status_verifikasi' => 'approved',
                    'catatan_verifikasi' => $notes,
                    'verified_by' => $userId,
                    'verified_at' => $currentTime
                ]);

                // update participant verification
                $registration->peserta->verifications()->update([
                    'status' => 'verified',
                    'verified_by' => $userId,
                    'verified_at' => $currentTime,
                    'notes' => $notes
                ]);

                $count++;
            }

            // Log bulk verification audit trail
            Log::info('Bulk verification performed', [
                'user_id' => $userId,
                'action' => $validated['action'],
                'count' => $count,
                'registrations' => $auditData
            ]);
        });

        $action = $validated['action'] === 'approve' ? 'disetujui' : 'ditolak';
        return back()->with('success', "{$count} pendaftaran berhasil {$action}.");
    }

    /**
     * Download a document
     */
    public function downloadDocument(DokumenPeserta $dokumen)
    {
        $this->requireVerificationAccess();

        $filePath = storage_path('app/public/' . $dokumen->path_file);

        if (!file_exists($filePath)) {
            abort(404, 'File tidak ditemukan.');
        }

        return response()->download($filePath, $dokumen->nama_file);
    }

    /**
     * Bulk verify registrations by status
     */
    public function bulkVerifyByStatus(Request $request)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'status_filter' => 'required|in:submitted,paid',
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:1000',
            'wilayah_filter' => 'nullable|exists:wilayah,id_wilayah',
            'golongan_filter' => 'nullable|exists:golongan,id_golongan'
        ]);

        $query = Pendaftaran::where('status_pendaftaran', $validated['status_filter']);

        // Apply additional filters
        if (!empty($validated['wilayah_filter'])) {
            $query->whereHas('peserta', function ($q) use ($validated) {
                $q->where('id_wilayah', $validated['wilayah_filter']);
            });
        }

        if (!empty($validated['golongan_filter'])) {
            $query->where('id_golongan', $validated['golongan_filter']);
        }

        $registrationIds = $query->pluck('id_pendaftaran')->toArray();

        if (empty($registrationIds)) {
            return back()->with('error', 'Tidak ada pendaftaran yang sesuai dengan kriteria filter.');
        }

        // Use existing bulk verify method
        $request->merge(['registration_ids' => $registrationIds]);
        return $this->bulkVerify($request);
    }

    /**
     * Apply filters to the query
     */
    private function applyFilters($query, Request $request): void
    {
        // NEW: Apply visibility constraints - only show participants that meet criteria
        $query->whereHas('peserta', function($q) {
            $q->where(function($participantQuery) {
                // Admin-registered participants (bypass regional verification)
                $participantQuery->where('registration_type', 'admin_daerah')
                    ->where('documents_complete', true);
            })->orWhere(function($participantQuery) {
                // Self-registered participants (require regional verification + complete documents)
                $participantQuery->where('registration_type', 'mandiri')
                    ->where('regional_verification_status', 'verified')
                    ->where('documents_complete', true);
            });
        });

        // Filter by status
        if ($request->filled('status')) {
            $query->where('status_pendaftaran', $request->status);
        }

        // Filter by wilayah
        if ($request->filled('wilayah')) {
            $query->whereHas('peserta', function ($q) use ($request) {
                $q->where('id_wilayah', $request->wilayah);
            });
        }

        // Filter by cabang lomba
        if ($request->filled('cabang_lomba')) {
            $query->whereHas('golongan', function ($q) use ($request) {
                $q->where('id_cabang', $request->cabang_lomba);
            });
        }

        // Filter by golongan
        if ($request->filled('golongan')) {
            $query->where('id_golongan', $request->golongan);
        }

        // Filter by date range
        if ($request->filled('date_from')) {
            $query->whereDate('created_at', '>=', $request->date_from);
        }

        if ($request->filled('date_to')) {
            $query->whereDate('created_at', '<=', $request->date_to);
        }

        // Filter by verification status
        if ($request->filled('verification_status')) {
            switch ($request->verification_status) {
                case 'pending_verification':
                    $query->whereIn('status_pendaftaran', ['submitted', 'paid']);
                    break;
                case 'verified':
                    $query->where('status_pendaftaran', 'verified');
                    break;
                case 'approved':
                    $query->where('status_pendaftaran', 'approved');
                    break;
                case 'rejected':
                    $query->where('status_pendaftaran', 'rejected');
                    break;
                case 'incomplete_documents':
                    $query->whereHas('peserta', function ($q) {
                        $q->whereHas('pendaftaran', function ($subQ) {
                            $subQ->whereDoesntHave('dokumenPeserta', function ($docQ) {
                                $docQ->where('status_verifikasi', 'approved');
                            });
                        });
                    });
                    break;
            }
        }

        // Filter by registration type
        if ($request->filled('registration_type')) {
            $query->whereHas('peserta', function ($q) use ($request) {
                $q->where('registration_type', $request->registration_type);
            });
        }

        // Filter by gender
        if ($request->filled('gender')) {
            $query->whereHas('peserta', function ($q) use ($request) {
                $q->where('jenis_kelamin', $request->gender);
            });
        }
    }

    /**
     * Build advanced query with optimized joins and subqueries
     */
    private function buildAdvancedQuery(Request $request)
    {
        $query = Pendaftaran::query()
            ->select([
                'pendaftaran.*',
                'peserta.nama_lengkap',
                'peserta.nik',
                'peserta.jenis_kelamin',
                'peserta.registration_type',
                'wilayah.nama_wilayah',
                'golongan.nama_golongan',
                'cabang_lomba.nama_cabang'
            ])
            ->join('peserta', 'pendaftaran.id_peserta', '=', 'peserta.id_peserta')
            ->join('wilayah', 'peserta.id_wilayah', '=', 'wilayah.id_wilayah')
            ->join('golongan', 'pendaftaran.id_golongan', '=', 'golongan.id_golongan')
            ->join('cabang_lomba', 'golongan.id_cabang', '=', 'cabang_lomba.id_cabang')
            ->with([
                'peserta.user',
                'peserta.wilayah',
                'golongan.cabangLomba',
                'verifiedBy',
                'approvedBy'
            ]);

        // Add document count subqueries for better performance
        $query->withCount([
            'dokumenPeserta as total_documents',
            'dokumenPeserta as approved_documents' => function ($q) {
                $q->where('status_verifikasi', 'approved');
            },
            'dokumenPeserta as pending_documents' => function ($q) {
                $q->where('status_verifikasi', 'pending');
            },
            'dokumenPeserta as rejected_documents' => function ($q) {
                $q->where('status_verifikasi', 'rejected');
            }
        ]);

        // Add participant verification count subqueries
        $query->withCount([
            'peserta.verifications as total_verifications',
            'peserta.verifications as completed_verifications' => function ($q) {
                $q->where('status', 'verified');
            }
        ]);

        return $query;
    }

    /**
     * Apply search with full-text capabilities
     */
    private function applyAdvancedSearch($query, string $search): void
    {
        $query->where(function ($q) use ($search) {
            $q->where('peserta.nama_lengkap', 'like', "%{$search}%")
              ->orWhere('peserta.nik', 'like', "%{$search}%")
              ->orWhere('pendaftaran.nomor_pendaftaran', 'like', "%{$search}%")
              ->orWhere('pendaftaran.nomor_peserta', 'like', "%{$search}%")
              ->orWhere('wilayah.nama_wilayah', 'like', "%{$search}%")
              ->orWhere('golongan.nama_golongan', 'like', "%{$search}%")
              ->orWhere('cabang_lomba.nama_cabang', 'like', "%{$search}%");
        });
    }

    /**
     * Get filter options for the dashboard
     */
    private function getFilterOptions(): array
    {
        return [
            'statuses' => [
                ['value' => 'draft', 'label' => 'Draft'],
                ['value' => 'submitted', 'label' => 'Submitted'],
                ['value' => 'payment_pending', 'label' => 'Payment Pending'],
                ['value' => 'paid', 'label' => 'Paid'],
                ['value' => 'verified', 'label' => 'Verified'],
                ['value' => 'approved', 'label' => 'Approved'],
                ['value' => 'rejected', 'label' => 'Rejected']
            ],
            'wilayah' => Wilayah::aktif()->orderBy('nama_wilayah')->get(['id_wilayah', 'nama_wilayah']),
            'cabangLomba' => CabangLomba::aktif()->orderBy('nama_cabang')->get(['id_cabang', 'nama_cabang']),
            'golongan' => Golongan::with('cabangLomba')->aktif()->orderBy('nama_golongan')->get()
        ];
    }

    /**
     * Get verification statistics (only for visible participants)
     */
    private function getVerificationStats(): array
    {
        // Base query with visibility constraints
        $baseQuery = Pendaftaran::whereHas('peserta', function($q) {
            $q->where(function($participantQuery) {
                // Admin-registered participants (bypass regional verification)
                $participantQuery->where('registration_type', 'admin_daerah')
                    ->where('documents_complete', true);
            })->orWhere(function($participantQuery) {
                // Self-registered participants (require regional verification + complete documents)
                $participantQuery->where('registration_type', 'mandiri')
                    ->where('regional_verification_status', 'verified')
                    ->where('documents_complete', true);
            });
        });

        return [
            'total_registrations' => (clone $baseQuery)->count(),
            'pending_verification' => (clone $baseQuery)->whereIn('status_pendaftaran', ['regional_verified', 'submitted', 'paid'])->count(),
            'verified' => (clone $baseQuery)->where('status_pendaftaran', 'verified')->count(),
            'approved' => (clone $baseQuery)->where('status_pendaftaran', 'approved')->count(),
            'rejected' => (clone $baseQuery)->where('status_pendaftaran', 'rejected')->count(),
            'documents_pending' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) {
                $q->where(function($participantQuery) {
                    $participantQuery->where('registration_type', 'admin_daerah')
                        ->where('documents_complete', true);
                })->orWhere(function($participantQuery) {
                    $participantQuery->where('registration_type', 'mandiri')
                        ->where('regional_verification_status', 'verified')
                        ->where('documents_complete', true);
                });
            })->where('status_verifikasi', 'pending')->count(),
            'documents_approved' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) {
                $q->where(function($participantQuery) {
                    $participantQuery->where('registration_type', 'admin_daerah')
                        ->where('documents_complete', true);
                })->orWhere(function($participantQuery) {
                    $participantQuery->where('registration_type', 'mandiri')
                        ->where('regional_verification_status', 'verified')
                        ->where('documents_complete', true);
                });
            })->where('status_verifikasi', 'approved')->count(),
            'documents_rejected' => DokumenPeserta::whereHas('pendaftaran.peserta', function($q) {
                $q->where(function($participantQuery) {
                    $participantQuery->where('registration_type', 'admin_daerah')
                        ->where('documents_complete', true);
                })->orWhere(function($participantQuery) {
                    $participantQuery->where('registration_type', 'mandiri')
                        ->where('regional_verification_status', 'verified')
                        ->where('documents_complete', true);
                });
            })->where('status_verifikasi', 'rejected')->count()
        ];
    }

    /**
     * Calculate verification progress for a registration
     */
    private function calculateVerificationProgress(Pendaftaran $pendaftaran): array
    {
        // Get required documents for this golongan
        $requiredDocuments = $pendaftaran->golongan->requiredDocumentTypes()->count();
        $submittedDocuments = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)->count();
        $verifiedDocuments = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->where('status_verifikasi', 'approved')->count();

        // Get participant verifications
        $requiredVerifications = VerificationType::where('is_required', true)->count();
        $completedVerifications = ParticipantVerification::where('id_peserta', $pendaftaran->id_peserta)
            ->where('status', 'verified')->count();

        return [
            'documents' => [
                'required' => $requiredDocuments,
                'submitted' => $submittedDocuments,
                'verified' => $verifiedDocuments,
                'percentage' => $requiredDocuments > 0 ? round(($verifiedDocuments / $requiredDocuments) * 100) : 0
            ],
            'verifications' => [
                'required' => $requiredVerifications,
                'completed' => $completedVerifications,
                'percentage' => $requiredVerifications > 0 ? round(($completedVerifications / $requiredVerifications) * 100) : 0
            ],
            'overall_percentage' => $this->calculateOverallProgress($requiredDocuments, $verifiedDocuments, $requiredVerifications, $completedVerifications)
        ];
    }

    /**
     * Calculate overall verification progress percentage
     */
    private function calculateOverallProgress(int $requiredDocs, int $verifiedDocs, int $requiredVerifs, int $completedVerifs): int
    {
        $totalRequired = $requiredDocs + $requiredVerifs;
        $totalCompleted = $verifiedDocs + $completedVerifs;

        return $totalRequired > 0 ? round(($totalCompleted / $totalRequired) * 100) : 0;
    }

    /**
     * Get document status for a registration
     */
    private function getDocumentStatus(Pendaftaran $pendaftaran): array
    {
        $documents = DokumenPeserta::where('id_pendaftaran', $pendaftaran->id_pendaftaran)
            ->selectRaw('status_verifikasi, COUNT(*) as count')
            ->groupBy('status_verifikasi')
            ->pluck('count', 'status_verifikasi')
            ->toArray();

        return [
            'pending' => $documents['pending'] ?? 0,
            'approved' => $documents['approved'] ?? 0,
            'rejected' => $documents['rejected'] ?? 0,
            'total' => array_sum($documents)
        ];
    }

    /**
     * Get participant verification status
     */
    private function getParticipantVerificationStatus(Pendaftaran $pendaftaran): array
    {
        $verifications = ParticipantVerification::where('id_peserta', $pendaftaran->id_peserta)
            ->selectRaw('status, COUNT(*) as count')
            ->groupBy('status')
            ->pluck('count', 'status')
            ->toArray();

        return [
            'pending' => $verifications['pending'] ?? 0,
            'verified' => $verifications['verified'] ?? 0,
            'rejected' => $verifications['rejected'] ?? 0,
            'total' => array_sum($verifications)
        ];
    }

    /**
     * Verify a document (approve or reject)
     */
    public function verifyDocument(Request $request, DokumenPeserta $dokumen)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'status_verifikasi' => 'required|in:approved,rejected',
            'catatan_verifikasi' => 'nullable|string|max:500'
        ]);

        $status = $validated['status_verifikasi'] === 'approved'
            ? VerificationService::STATUS_VERIFIED
            : VerificationService::STATUS_REJECTED;

        $this->verificationService->verifyDocument(
            $dokumen,
            $status,
            $validated['catatan_verifikasi']
        );

        $statusText = $this->verificationService->getStatusMessage($status, 'document');

        return back()->with('success', "Dokumen {$statusText}.");
    }

    /**
     * Bulk verify documents
     */
    public function bulkVerifyDocuments(Request $request)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'document_ids' => 'required|array|min:1',
            'document_ids.*' => 'exists:dokumen_peserta,id_dokumen',
            'action' => 'required|in:approve,reject',
            'notes' => 'nullable|string|max:500'
        ]);

        $status = $validated['action'] === 'approve' ? 'approved' : 'rejected';
        $count = 0;

        DB::transaction(function () use ($validated, $status, &$count) {
            $documents = DokumenPeserta::whereIn('id_dokumen', $validated['document_ids'])->get();

            foreach ($documents as $document) {
                $document->update([
                    'status_verifikasi' => $status,
                    'catatan_verifikasi' => $validated['notes'],
                    'verified_by' => Auth::id(),
                    'verified_at' => now()
                ]);
                $count++;
            }
        });

        $action = $validated['action'] === 'approve' ? 'disetujui' : 'ditolak';
        return back()->with('success', "{$count} dokumen berhasil {$action}.");
    }

    /**
     * Verify participant data (NIK, personal info, etc.)
     */
    public function verifyParticipantData(Request $request, Pendaftaran $pendaftaran)
    {
        $this->requireVerificationAccess();

        $validated = $request->validate([
            'verification_type_id' => 'required|exists:verification_types,id_verification_type',
            'status' => 'required|in:verified,rejected',
            'notes' => 'nullable|string|max:1000',
            'verification_data' => 'nullable|array'
        ]);

        // Create or update participant verification
        ParticipantVerification::updateOrCreate(
            [
                'id_peserta' => $pendaftaran->id_peserta,
                'verification_type_id' => $validated['verification_type_id']
            ],
            [
                'status' => $validated['status'],
                'verified_by' => Auth::id(),
                'verified_at' => now(),
                'notes' => $validated['notes'],
                'verification_data' => $validated['verification_data']
            ]
        );

        $message = $validated['status'] === 'verified'
            ? 'Data peserta berhasil diverifikasi.'
            : 'Data peserta ditolak.';

        return back()->with('success', $message);
    }
}
