<template>
  <Dialog :open="show" @update:open="emit('update:show', $event)">
    <DialogContent class="sm:max-w-md">
      <DialogHeader>
        <DialogTitle class="flex items-center gap-2">
          <Icon name="download" class="w-5 h-5" />
          Export {{ title }}
        </DialogTitle>
        <DialogDescription>
          Pilih format dan filter untuk export data {{ title.toLowerCase() }}.
        </DialogDescription>
      </DialogHeader>

      <div class="space-y-4">
        <!-- Format Selection -->
        <div class="space-y-2">
          <Label>Format File</Label>
          <div class="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedFormat === 'xlsx' }"
              @click="selectedFormat = 'xlsx'"
            >
              <Icon name="file-spreadsheet" class="w-4 h-4 mr-2" />
              Excel (.xlsx)
            </Button>
            <Button
              variant="outline"
              size="sm"
              :class="{ 'bg-primary text-primary-foreground': selectedFormat === 'csv' }"
              @click="selectedFormat = 'csv'"
            >
              <Icon name="file-text" class="w-4 h-4 mr-2" />
              CSV (.csv)
            </Button>
          </div>
        </div>

        <!-- Filters -->
        <div v-if="showFilters" class="space-y-3">
          <Label>Filter Data (Opsional)</Label>

          <!-- Search Filter -->
          <div class="space-y-1">
            <Label for="search" class="text-sm">Pencarian</Label>
            <Input
              id="search"
              v-model="filters.search"
              placeholder="Cari berdasarkan nama atau kode..."
            />
          </div>

          <!-- Status Filter -->
          <div class="space-y-1">
            <Label for="status" class="text-sm">Status</Label>
            <Select v-model="filters.status">
              <SelectTrigger>
                <SelectValue placeholder="Semua Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Status</SelectItem>
                <SelectItem value="aktif">Aktif</SelectItem>
                <SelectItem value="non_aktif">Non Aktif</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <!-- Cabang Filter (for Golongan) -->
          <div v-if="showCabangFilter" class="space-y-1">
            <Label for="cabang" class="text-sm">Cabang Lomba</Label>
            <Select v-model="filters.id_cabang">
              <SelectTrigger>
                <SelectValue placeholder="Semua Cabang" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="">Semua Cabang</SelectItem>
                <SelectItem
                  v-for="cabang in cabangLomba"
                  :key="cabang.id_cabang"
                  :value="cabang.id_cabang.toString()"
                >
                  {{ cabang.nama_cabang }}
                </SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <DialogFooter class="flex justify-between">
        <Button variant="outline" @click="emit('update:show', false)">
          Batal
        </Button>
        <Button @click="handleExport" :disabled="isExporting">
          <Icon v-if="isExporting" name="loader" class="w-4 h-4 mr-2 animate-spin" />
          <Icon v-else name="download" class="w-4 h-4 mr-2" />
          {{ isExporting ? 'Mengexport...' : 'Export' }}
        </Button>
      </DialogFooter>
    </DialogContent>
  </Dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import Icon from '@/components/Icon.vue'

// Props
interface Props {
  show: boolean
  title: string
  exportUrl: string
  showFilters?: boolean
  showCabangFilter?: boolean
  cabangLomba?: Array<{ id_cabang: number; nama_cabang: string }>
}

const props = withDefaults(defineProps<Props>(), {
  showFilters: true,
  showCabangFilter: false,
  cabangLomba: () => []
})

// Emits
const emit = defineEmits<{
  'update:show': [value: boolean]
}>()

// Reactive data
const selectedFormat = ref('xlsx')
const isExporting = ref(false)
const filters = ref({
  search: '',
  status: '',
  id_cabang: ''
})

// Methods
const handleExport = async () => {
  isExporting.value = true

  try {
    // Build query parameters
    const params = new URLSearchParams({
      format: selectedFormat.value,
      ...Object.fromEntries(
        Object.entries(filters.value).filter(([_, value]) => value !== '')
      )
    })

    // Create download link
    const url = `${props.exportUrl}?${params.toString()}`
    window.open(url, '_blank')

    // Close modal after short delay
    setTimeout(() => {
      emit('update:show', false)
    }, 1000)

  } catch (error) {
    console.error('Export error:', error)
  } finally {
    isExporting.value = false
  }
}
</script>
